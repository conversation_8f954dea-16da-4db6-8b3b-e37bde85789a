2025-08-25 09:45:05.206 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 09:45:05.509 -03:00 [INF] Hosting environment: Production
2025-08-25 09:45:05.511 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 09:45:09.591 -03:00 [INF] Conectando à API Deriv...
2025-08-25 09:45:10.693 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 09:45:21.206 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 09:45:21.220 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 09:45:21.294 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 09:45:21.295 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 09:45:21.788 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 09:45:21.792 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 09:45:21.793 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 09:45:21.794 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 09:45:21.794 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 09:45:21.794 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 09:45:21.794 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 09:45:21.794 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 09:45:21.794 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 09:45:21.831 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 09:45:21.831 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 09:45:21.832 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 09:45:21.832 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 09:45:21.832 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 09:45:21.832 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 09:45:21.832 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 09:45:21.832 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 09:45:21.833 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 09:45:21.865 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 09:45:21.866 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 09:50:53.277 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 09:50:53.281 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 09:50:53.333 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 09:51:02.015 -03:00 [INF] [DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population
2025-08-25 09:51:02.029 -03:00 [INF] [TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at 09:51:02.022
2025-08-25 09:51:02.029 -03:00 [INF] [DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild
2025-08-25 09:51:02.401 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 344
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 420
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass219_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1263
2025-08-25 09:51:02.441 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 344
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 420
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass219_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1263
2025-08-25 09:51:02.442 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 344
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 420
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass219_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1263
2025-08-25 09:51:02.444 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 344
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 420
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass219_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1263
2025-08-25 09:51:02.444 -03:00 [ERR] [DEBUG] HOT POOL AGGRESSIVE: Exception in level 6
System.Exception: Please enter a stake amount that's at least 0.35.
   at Excalibur.Services.DerivApiService.SendFastRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 344
   at Excalibur.Services.DerivApiService.GetFastProposalAsync(ProposalRequest request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 420
   at Excalibur.ViewModels.MainViewModel.<>c__DisplayClass219_0.<<PopulateHotProposalPoolImmediate>b__0>d.MoveNext() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 1263
2025-08-25 09:51:02.474 -03:00 [INF] [TIMING] HOT POOL AGGRESSIVE: Population completed in 423,848ms. Pool contains 0 proposals GUARANTEED ready. Levels: []
2025-08-25 09:51:02.474 -03:00 [WRN] [DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: False, Second: False. Current: 0
2025-08-25 09:51:02.475 -03:00 [INF] [DEBUG] Fast Martingale READY: 0 proposals pre-calculated and ready for instant execution
2025-08-25 09:56:21.673 -03:00 [WRN] Conexão perdida: Lost
2025-08-25 09:56:22.227 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-08-25 09:56:22.556 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 09:56:22.558 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 09:56:22.886 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 09:56:22.886 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 09:56:22.912 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-08-25 09:56:22.920 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 09:56:22.921 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 09:56:22.921 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 09:56:22.921 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 09:56:22.922 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 09:56:22.922 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 09:56:22.922 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 09:56:22.935 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 1/5
2025-08-25 09:56:22.936 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 2/5
2025-08-25 09:56:22.936 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 3/5
2025-08-25 09:56:22.936 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 4/5
2025-08-25 09:56:22.936 -03:00 [WRN] [DEBUG] Tipo de contrato ASIAND não encontrado na lista atual após 5 tentativas
2025-08-25 09:56:22.936 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=
2025-08-25 10:03:30.561 -03:00 [WRN] Conexão não responsiva há 2,9s - forçando reconexão URGENTE para Fast Martingale
2025-08-25 10:03:30.716 -03:00 [WRN] Conexão perdida: ByUser
2025-08-25 10:03:37.923 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 10:03:38.302 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 10:03:38.302 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 10:03:38.747 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 10:03:38.747 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 10:03:38.761 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 10:03:38.761 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 10:03:38.761 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 10:03:38.762 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 10:03:38.762 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 10:03:38.762 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 10:03:38.762 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 10:03:38.784 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=
2025-08-25 10:04:04.303 -03:00 [WRN] Conexão não responsiva há 2,9s - forçando reconexão URGENTE para Fast Martingale
2025-08-25 10:04:04.443 -03:00 [WRN] Conexão perdida: ByUser
2025-08-25 10:04:45.450 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 10:04:45.817 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 10:04:45.818 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 10:04:46.662 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 10:04:46.663 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 10:04:46.671 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 10:04:46.672 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 10:04:46.672 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 10:04:46.672 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 10:04:46.672 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 10:04:46.672 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 10:04:46.672 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 10:04:46.695 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=
2025-08-25 10:04:53.818 -03:00 [WRN] Conexão não responsiva há 3,3s - forçando reconexão URGENTE para Fast Martingale
2025-08-25 10:04:53.955 -03:00 [WRN] Conexão perdida: ByUser
2025-08-25 10:05:05.369 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 10:05:05.755 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 10:05:05.755 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 10:05:06.600 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 10:05:06.600 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 10:05:06.617 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 10:05:06.617 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 10:05:06.617 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 10:05:06.617 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 10:05:06.618 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 10:05:06.618 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 10:05:06.618 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 10:05:06.641 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=
2025-08-25 10:05:11.761 -03:00 [WRN] Conexão não responsiva há 2,9s - forçando reconexão URGENTE para Fast Martingale
2025-08-25 10:05:11.910 -03:00 [WRN] Conexão perdida: ByUser
2025-08-25 10:05:21.075 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 10:05:21.475 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 10:05:21.475 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 10:05:21.931 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 10:05:21.931 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 10:05:21.941 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 10:05:21.941 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 10:05:21.941 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 10:05:21.941 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 10:05:21.942 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 10:05:21.942 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 10:05:21.942 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 10:05:21.950 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=
2025-08-25 10:17:29.491 -03:00 [WRN] Conexão não responsiva há 3,9s - forçando reconexão URGENTE para Fast Martingale
2025-08-25 10:17:29.642 -03:00 [WRN] Conexão perdida: ByUser
2025-08-25 10:17:33.721 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 10:17:34.257 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 10:17:34.258 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 10:17:34.764 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 10:17:34.764 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 10:17:34.773 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 10:17:34.774 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 10:17:34.774 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 10:17:34.774 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 10:17:34.774 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 10:17:34.774 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 10:17:34.774 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 10:17:34.795 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=
2025-08-25 10:24:18.712 -03:00 [WRN] Conexão perdida: Lost
2025-08-25 10:24:19.259 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-08-25 10:24:19.695 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 10:24:19.946 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 10:24:20.264 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 10:24:20.264 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 10:24:20.268 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 10:24:20.269 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 10:24:20.269 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 10:24:20.269 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 10:24:20.269 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 10:24:20.269 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 10:24:20.269 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 10:24:20.273 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=
2025-08-25 10:32:51.109 -03:00 [WRN] Conexão perdida: Lost
2025-08-25 10:32:51.368 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-08-25 10:32:51.743 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 10:32:51.744 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 10:32:51.942 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 10:32:51.943 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 10:32:51.951 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 10:32:51.951 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 10:32:51.951 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 10:32:51.951 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 10:32:51.951 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 10:32:51.951 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 10:32:51.952 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 10:32:51.958 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=
2025-08-25 10:46:31.156 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 10:46:31.157 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 10:46:31.157 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 10:49:40.550 -03:00 [INF] Application is shutting down...
2025-08-25 11:12:08.900 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 11:12:09.013 -03:00 [INF] Hosting environment: Production
2025-08-25 11:12:09.015 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 11:12:11.787 -03:00 [INF] Conectando à API Deriv...
2025-08-25 11:12:12.749 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 11:12:19.089 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 11:12:19.105 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 11:12:19.171 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 11:12:19.172 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 11:12:19.626 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 11:12:19.629 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 11:12:19.630 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 11:12:19.630 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 11:12:19.630 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 11:12:19.630 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 11:12:19.631 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 11:12:19.631 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 11:12:19.631 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 11:12:19.637 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 11:12:19.760 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 11:12:19.761 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 11:12:19.761 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 11:12:19.761 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 11:12:19.761 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 11:12:19.762 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 11:12:19.762 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 11:12:19.762 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 11:12:19.762 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 11:12:19.765 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 11:13:24.268 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 11:13:24.270 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 11:13:24.298 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 11:15:11.172 -03:00 [WRN] Conexão não responsiva há 3,4s - forçando reconexão URGENTE para Fast Martingale
2025-08-25 11:15:26.223 -03:00 [WRN] Conexão perdida: ByUser
2025-08-25 11:15:27.671 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 11:15:28.210 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 11:15:28.211 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 11:15:28.792 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 11:15:28.792 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 11:15:28.812 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-08-25 11:15:28.818 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 11:15:28.819 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 11:15:28.819 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 11:15:28.819 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 11:15:28.819 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 11:15:28.819 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 11:15:28.819 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 11:15:29.230 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 1/10
2025-08-25 11:15:29.331 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 2/10
2025-08-25 11:15:29.431 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 3/10
2025-08-25 11:15:29.536 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 11:15:29.536 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 11:15:29.536 -03:00 [INF] [DEBUG] Tipo de contrato restaurado: ASIAND
2025-08-25 11:15:29.536 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=ASIAND
2025-08-25 11:15:29.536 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 11:21:17.689 -03:00 [WRN] Conexão perdida: Lost
2025-08-25 11:21:18.245 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-08-25 11:21:18.611 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 11:21:18.612 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 11:21:18.873 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 11:21:18.873 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 11:21:18.884 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-08-25 11:21:18.889 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 11:21:18.889 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 11:21:18.889 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 11:21:18.890 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 11:21:18.890 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 11:21:18.890 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 11:21:18.890 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 11:21:19.310 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 1/10
2025-08-25 11:21:19.414 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 11:21:19.414 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 11:21:19.414 -03:00 [INF] [DEBUG] Tipo de contrato restaurado: ASIAND
2025-08-25 11:21:19.414 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=ASIAND
2025-08-25 11:21:19.417 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 11:27:38.554 -03:00 [INF] Application is shutting down...
2025-08-25 11:29:01.092 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 11:29:01.238 -03:00 [INF] Hosting environment: Production
2025-08-25 11:29:01.240 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 11:29:03.052 -03:00 [INF] Conectando à API Deriv...
2025-08-25 11:29:12.654 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 11:29:13.210 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 11:29:13.222 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 11:29:13.285 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 11:29:13.285 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 11:29:13.576 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 11:29:13.581 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 11:29:13.583 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 11:29:13.584 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 11:29:13.585 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 11:29:13.585 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 11:29:13.586 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 11:29:13.586 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 11:29:13.586 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 11:29:13.590 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 11:29:13.590 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 11:29:13.593 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 11:29:13.594 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 11:29:13.594 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 11:29:13.595 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 11:29:13.595 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 11:29:13.595 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 11:29:13.596 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 11:29:13.600 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 11:29:13.601 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 11:29:23.926 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 11:29:23.930 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 11:29:23.985 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 11:32:52.235 -03:00 [INF] Application is shutting down...
2025-08-25 11:39:55.394 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 11:39:55.573 -03:00 [INF] Hosting environment: Production
2025-08-25 11:39:55.575 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 11:39:56.759 -03:00 [INF] Conectando à API Deriv...
2025-08-25 11:40:03.954 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 11:40:04.568 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 11:40:04.578 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 11:40:04.619 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 11:40:04.619 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 11:40:05.504 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 11:40:05.509 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 11:40:05.510 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 11:40:05.511 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 11:40:05.511 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 11:40:05.511 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 11:40:05.511 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 11:40:05.511 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 11:40:05.511 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 11:40:05.518 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 11:40:05.617 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 11:40:05.618 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 11:40:05.618 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 11:40:05.618 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 11:40:05.618 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 11:40:05.618 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 11:40:05.618 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 11:40:05.618 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 11:40:05.618 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 11:40:05.619 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 11:40:16.619 -03:00 [WRN] Conexão não responsiva há 3,7s - forçando reconexão URGENTE para Fast Martingale
2025-08-25 11:40:17.925 -03:00 [WRN] Conexão perdida: ByUser
2025-08-25 11:40:19.193 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 11:40:19.728 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 11:40:19.729 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 11:40:20.160 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 11:40:20.160 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 11:40:20.161 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 11:40:20.161 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 11:40:20.161 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 11:40:20.161 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 11:40:20.161 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 11:40:20.161 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 11:40:20.162 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 11:40:20.162 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 11:40:41.381 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 11:40:41.385 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 11:40:41.425 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 11:46:37.662 -03:00 [INF] Application is shutting down...
2025-08-25 12:59:24.552 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 12:59:24.712 -03:00 [INF] Hosting environment: Production
2025-08-25 12:59:24.714 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 12:59:28.590 -03:00 [INF] Conectando à API Deriv...
2025-08-25 12:59:42.609 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 12:59:43.655 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 12:59:43.667 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 12:59:43.786 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 12:59:43.787 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 12:59:44.127 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 12:59:44.154 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 12:59:44.155 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 12:59:44.156 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 12:59:44.156 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 12:59:44.156 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 12:59:44.156 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 12:59:44.156 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 12:59:44.156 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 12:59:44.166 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 12:59:44.166 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 12:59:44.166 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 12:59:44.167 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 12:59:44.167 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 12:59:44.167 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 12:59:44.167 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 12:59:44.167 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 12:59:44.167 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 12:59:44.171 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 12:59:44.172 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 13:01:33.850 -03:00 [INF] Application is shutting down...
2025-08-25 13:06:25.033 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 13:06:25.183 -03:00 [INF] Hosting environment: Production
2025-08-25 13:06:25.185 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 13:06:26.622 -03:00 [INF] Conectando à API Deriv...
2025-08-25 13:06:36.280 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 13:06:37.108 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 13:06:37.122 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 13:06:37.231 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 13:06:37.232 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 13:06:37.600 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 13:06:37.603 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 13:06:37.604 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 13:06:37.605 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 13:06:37.605 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 13:06:37.605 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 13:06:37.605 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 13:06:37.605 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 13:06:37.605 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 13:06:37.609 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 13:06:37.609 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 13:06:37.609 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 13:06:37.609 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 13:06:37.610 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 13:06:37.610 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 13:06:37.610 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 13:06:37.610 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 13:06:37.610 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 13:06:37.613 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 13:06:37.613 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 13:07:26.933 -03:00 [WRN] Conexão perdida: Lost
2025-08-25 13:07:27.445 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-08-25 13:07:27.805 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 13:07:27.806 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 13:07:28.278 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 13:07:28.279 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 13:07:28.279 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 13:07:28.279 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 13:07:28.280 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 13:07:28.280 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 13:07:28.280 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 13:07:28.280 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 13:07:28.280 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 13:07:28.280 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 13:32:47.807 -03:00 [WRN] Conexão não responsiva há 3,6s - forçando reconexão URGENTE para Fast Martingale
2025-08-25 13:32:57.477 -03:00 [WRN] Conexão perdida: ByUser
2025-08-25 13:32:58.986 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 13:32:59.457 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 13:32:59.457 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 13:33:00.146 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 13:33:00.146 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 13:33:00.147 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 13:33:00.147 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 13:33:00.147 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 13:33:00.147 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 13:33:00.147 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 13:33:00.147 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 13:33:00.147 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 13:33:00.148 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 13:42:33.012 -03:00 [INF] Application is shutting down...
2025-08-25 13:42:45.539 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 13:42:45.658 -03:00 [INF] Hosting environment: Production
2025-08-25 13:42:45.659 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 13:42:46.943 -03:00 [INF] Conectando à API Deriv...
2025-08-25 13:42:53.497 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 13:42:54.286 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 13:42:54.296 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 13:42:54.356 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 13:42:54.356 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 13:42:54.971 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 13:42:54.976 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 13:42:54.977 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 13:42:54.978 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 13:42:54.978 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 13:42:54.978 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 13:42:54.979 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 13:42:54.979 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 13:42:54.979 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 13:42:54.987 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 13:42:54.988 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 13:42:54.988 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 13:42:54.988 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 13:42:54.989 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 13:42:54.989 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 13:42:54.989 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 13:42:54.989 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 13:42:54.989 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 13:42:54.995 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 13:42:54.995 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 13:43:04.934 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 13:43:04.939 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 13:43:04.972 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 13:44:46.441 -03:00 [INF] Application is shutting down...
2025-08-25 13:50:19.141 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 13:50:19.278 -03:00 [INF] Hosting environment: Production
2025-08-25 13:50:19.280 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 13:50:21.344 -03:00 [INF] Conectando à API Deriv...
2025-08-25 13:50:31.582 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 13:50:32.435 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 13:50:32.447 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 13:50:32.551 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 13:50:32.551 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 13:50:33.230 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 13:50:33.235 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 13:50:33.236 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 13:50:33.237 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 13:50:33.237 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 13:50:33.237 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 13:50:33.237 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 13:50:33.237 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 13:50:33.238 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 13:50:33.249 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 13:50:33.303 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 13:50:33.303 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 13:50:33.304 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 13:50:33.304 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 13:50:33.304 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 13:50:33.304 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 13:50:33.304 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 13:50:33.304 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 13:50:33.304 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 13:50:33.307 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 13:51:13.260 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 13:51:13.266 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 13:51:13.314 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 13:58:49.179 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 13:58:49.426 -03:00 [INF] Hosting environment: Production
2025-08-25 13:58:49.429 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 13:58:51.777 -03:00 [INF] Conectando à API Deriv...
2025-08-25 13:58:53.013 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 13:58:54.075 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 13:58:54.088 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 13:58:54.187 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 13:58:54.187 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 13:58:54.518 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 13:58:54.523 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 13:58:54.525 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 13:58:54.526 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 13:58:54.526 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 13:58:54.526 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 13:58:54.526 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 13:58:54.527 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 13:58:54.527 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 13:58:54.533 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 13:58:54.533 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 13:58:54.534 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 13:58:54.534 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 13:58:54.534 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 13:58:54.534 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 13:58:54.535 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 13:58:54.535 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 13:58:54.535 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 13:58:54.541 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 13:58:54.541 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 13:59:28.171 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 13:59:28.175 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 13:59:28.232 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 14:01:29.360 -03:00 [INF] Application is shutting down...
2025-08-25 14:03:44.085 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 14:03:44.210 -03:00 [INF] Hosting environment: Production
2025-08-25 14:03:44.212 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 14:03:45.860 -03:00 [INF] Conectando à API Deriv...
2025-08-25 14:03:54.188 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 14:03:54.888 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:03:54.899 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:03:55.015 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:03:55.016 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:03:55.536 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:03:55.540 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:03:55.541 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:03:55.541 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:03:55.542 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:03:55.542 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:03:55.542 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:03:55.542 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:03:55.542 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:03:55.550 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:03:55.624 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:03:55.625 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:03:55.625 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:03:55.625 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:03:55.625 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:03:55.625 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:03:55.625 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:03:55.625 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:03:55.626 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:03:55.626 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:04:09.880 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 14:04:09.886 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 14:04:09.927 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 14:04:57.015 -03:00 [WRN] Conexão não responsiva há 2,8s - forçando reconexão URGENTE para Fast Martingale
2025-08-25 14:04:58.061 -03:00 [WRN] Conexão perdida: ByUser
2025-08-25 14:05:00.160 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 14:05:00.874 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:05:00.875 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:05:01.979 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:05:01.980 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:05:02.007 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-08-25 14:05:02.011 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:05:02.011 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:05:02.012 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:05:02.012 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:05:02.012 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:05:02.013 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:05:02.013 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:05:02.426 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 1/10
2025-08-25 14:05:02.527 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 2/10
2025-08-25 14:05:02.628 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 3/10
2025-08-25 14:05:02.731 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 4/10
2025-08-25 14:05:02.833 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 5/10
2025-08-25 14:05:02.934 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 14:05:02.934 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 14:05:02.934 -03:00 [INF] [DEBUG] Tipo de contrato restaurado: ASIAND
2025-08-25 14:05:02.934 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=ASIAND
2025-08-25 14:05:02.934 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 14:05:16.781 -03:00 [INF] Application is shutting down...
2025-08-25 14:08:31.744 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 14:08:31.996 -03:00 [INF] Hosting environment: Production
2025-08-25 14:08:32.001 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 14:08:35.345 -03:00 [INF] Conectando à API Deriv...
2025-08-25 14:08:45.926 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 14:08:47.027 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:08:47.042 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:08:47.208 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:08:47.209 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:08:48.380 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:08:48.384 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:08:48.388 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:08:48.390 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:08:48.390 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:08:48.390 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:08:48.391 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:08:48.391 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:08:48.391 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:08:48.424 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:08:48.425 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:08:48.426 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:08:48.426 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:08:48.426 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:08:48.426 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:08:48.426 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:08:48.427 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:08:48.427 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:08:48.430 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:08:48.431 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:09:49.845 -03:00 [WRN] Conexão perdida: Lost
2025-08-25 14:09:50.382 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-08-25 14:09:50.821 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:09:50.822 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:09:51.114 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:09:51.115 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:09:51.115 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:09:51.116 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:09:51.116 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:09:51.116 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:09:51.116 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:09:51.116 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:09:51.116 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:09:51.117 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:13:39.386 -03:00 [INF] Application is shutting down...
2025-08-25 14:14:11.221 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 14:14:11.349 -03:00 [INF] Hosting environment: Production
2025-08-25 14:14:11.350 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 14:14:12.583 -03:00 [INF] Conectando à API Deriv...
2025-08-25 14:14:21.877 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 14:14:22.568 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:14:22.580 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:14:22.682 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:14:22.683 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:14:23.058 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:14:23.065 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:14:23.066 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:14:23.067 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:14:23.067 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:14:23.067 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:14:23.067 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:14:23.067 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:14:23.068 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:14:23.074 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:14:23.075 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:14:23.076 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:14:23.076 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:14:23.076 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:14:23.076 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:14:23.076 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:14:23.076 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:14:23.077 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:14:23.082 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:14:23.083 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:14:38.123 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 14:14:38.128 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 14:14:38.176 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 14:18:47.309 -03:00 [INF] Application is shutting down...
2025-08-25 14:18:47.367 -03:00 [ERR] Erro ao processar mensagem da API.
System.NullReferenceException: Object reference not set to an instance of an object.
   at Excalibur.ViewModels.MainViewModel.OnPingUpdated(Int64 newPing) in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 591
   at Excalibur.Services.DerivApiService.ProcessMessage(String jsonMessage) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 267
2025-08-25 14:26:24.892 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 14:26:25.033 -03:00 [INF] Hosting environment: Production
2025-08-25 14:26:25.035 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 14:26:26.963 -03:00 [INF] Conectando à API Deriv...
2025-08-25 14:26:28.231 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 14:26:38.174 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:26:38.184 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:26:38.283 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:26:38.284 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:26:38.820 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:26:38.824 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:26:38.825 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:26:38.826 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:26:38.826 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:26:38.827 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:26:38.828 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:26:38.828 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:26:38.828 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:26:38.851 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:26:38.851 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:26:38.854 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:26:38.854 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:26:38.855 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:26:38.855 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:26:38.856 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:26:38.857 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:26:38.857 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:26:38.863 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:26:38.863 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:27:10.742 -03:00 [INF] Application is shutting down...
2025-08-25 14:27:24.254 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 14:27:24.341 -03:00 [INF] Hosting environment: Production
2025-08-25 14:27:24.342 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 14:27:25.218 -03:00 [INF] Conectando à API Deriv...
2025-08-25 14:27:30.971 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 14:27:31.742 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:27:31.752 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:27:31.853 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:27:31.854 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:27:32.167 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:27:32.170 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:27:32.171 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:27:32.172 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:27:32.172 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:27:32.172 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:27:32.173 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:27:32.173 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:27:32.173 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:27:32.182 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:27:32.183 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:27:32.185 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:27:32.185 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:27:32.186 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:27:32.187 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:27:32.187 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:27:32.187 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:27:32.187 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:27:32.192 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:27:32.193 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:27:41.844 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 14:27:41.847 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 14:27:41.883 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 14:33:52.559 -03:00 [INF] Application is shutting down...
2025-08-25 14:49:59.559 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 14:49:59.705 -03:00 [INF] Hosting environment: Production
2025-08-25 14:49:59.707 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 14:50:01.329 -03:00 [INF] Conectando à API Deriv...
2025-08-25 14:50:09.626 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 14:50:10.509 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:50:10.519 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:50:10.621 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:50:10.622 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:50:11.178 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:50:11.182 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:50:11.184 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:50:11.185 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:50:11.186 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:50:11.187 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:50:11.187 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:50:11.187 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:50:11.188 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:50:11.198 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:50:11.199 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:50:11.200 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:50:11.201 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:50:11.201 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:50:11.202 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:50:11.202 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:50:11.202 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:50:11.202 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:50:11.208 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:50:11.209 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:50:21.095 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 14:50:21.103 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 14:50:21.148 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 14:52:42.496 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 14:52:42.815 -03:00 [INF] Hosting environment: Production
2025-08-25 14:52:42.820 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 14:52:44.815 -03:00 [INF] Conectando à API Deriv...
2025-08-25 14:52:56.089 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 14:52:57.002 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:52:57.012 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:52:57.110 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 14:52:57.111 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 14:52:57.672 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:52:57.678 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:52:57.680 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:52:57.681 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:52:57.681 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:52:57.682 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:52:57.683 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:52:57.683 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:52:57.683 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:52:57.776 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 14:52:57.777 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 14:52:57.781 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 14:52:57.781 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 14:52:57.782 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 14:52:57.782 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 14:52:57.783 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 14:52:57.783 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 14:52:57.783 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 14:52:57.790 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:52:57.790 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 14:53:10.661 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 14:53:10.668 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 14:53:10.715 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 14:57:37.246 -03:00 [INF] Application is shutting down...
2025-08-25 15:00:48.653 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 15:00:48.809 -03:00 [INF] Hosting environment: Production
2025-08-25 15:00:48.811 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 15:00:51.213 -03:00 [INF] Conectando à API Deriv...
2025-08-25 15:01:02.580 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 15:01:03.421 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 15:01:03.433 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 15:01:03.530 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 15:01:03.531 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 15:01:04.172 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 15:01:04.177 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 15:01:04.178 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 15:01:04.178 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 15:01:04.179 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 15:01:04.179 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 15:01:04.179 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 15:01:04.179 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 15:01:04.179 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 15:01:04.187 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 15:01:04.187 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 15:01:04.188 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 15:01:04.188 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 15:01:04.188 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 15:01:04.188 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 15:01:04.188 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 15:01:04.188 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 15:01:04.189 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 15:01:04.193 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 15:01:04.194 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 15:01:51.827 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 15:01:51.832 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 15:01:51.874 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 15:05:04.146 -03:00 [INF] Application is shutting down...
2025-08-25 15:07:02.539 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 15:07:02.670 -03:00 [INF] Hosting environment: Production
2025-08-25 15:07:02.672 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 15:07:04.113 -03:00 [INF] Conectando à API Deriv...
2025-08-25 15:07:10.995 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 15:07:11.801 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 15:07:11.809 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 15:07:11.871 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 15:07:11.871 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 15:07:12.439 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 15:07:12.442 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 15:07:12.442 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 15:07:12.443 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 15:07:12.443 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 15:07:12.443 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 15:07:12.443 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 15:07:12.443 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 15:07:12.443 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 15:07:12.447 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 15:07:12.448 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 15:07:12.448 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 15:07:12.448 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 15:07:12.448 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 15:07:12.448 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 15:07:12.449 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 15:07:12.449 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 15:07:12.449 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 15:07:12.452 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 15:07:12.454 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 15:11:48.197 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 15:11:48.202 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 15:11:48.240 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 15:12:22.239 -03:00 [INF] Application is shutting down...
2025-08-25 15:16:21.759 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 15:16:21.923 -03:00 [INF] Hosting environment: Production
2025-08-25 15:16:21.925 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 15:16:23.157 -03:00 [INF] Conectando à API Deriv...
2025-08-25 15:16:31.654 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 15:16:32.527 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 15:16:32.538 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 15:16:32.616 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 15:16:32.617 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 15:16:33.239 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 15:16:33.245 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 15:16:33.246 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 15:16:33.246 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 15:16:33.246 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 15:16:33.247 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 15:16:33.247 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 15:16:33.247 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 15:16:33.247 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 15:16:33.258 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 15:16:33.345 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 15:16:33.345 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 15:16:33.346 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 15:16:33.346 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 15:16:33.346 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 15:16:33.346 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 15:16:33.346 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 15:16:33.346 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 15:16:33.346 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 15:16:33.347 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 15:21:28.134 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 15:21:28.137 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 15:21:28.162 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 15:35:57.746 -03:00 [INF] Application is shutting down...
2025-08-25 15:49:23.142 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 15:49:23.275 -03:00 [INF] Hosting environment: Production
2025-08-25 15:49:23.277 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 15:49:24.604 -03:00 [INF] Conectando à API Deriv...
2025-08-25 15:49:32.386 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 15:49:33.209 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 15:49:33.221 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 15:49:33.313 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 15:49:33.313 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 15:49:33.919 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 15:49:33.922 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 15:49:33.923 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 15:49:33.923 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 15:49:33.923 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 15:49:33.923 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 15:49:33.923 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 15:49:33.923 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 15:49:33.923 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 15:49:33.930 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 15:49:33.968 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 15:49:33.968 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 15:49:33.969 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 15:49:33.969 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 15:49:33.969 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 15:49:33.969 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 15:49:33.969 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 15:49:33.969 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 15:49:33.969 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 15:49:33.970 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 15:50:46.730 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 15:50:46.735 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 15:50:46.763 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 15:52:31.916 -03:00 [INF] Application is shutting down...
2025-08-25 16:04:57.391 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:04:57.504 -03:00 [INF] Hosting environment: Production
2025-08-25 16:04:57.505 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:04:58.684 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:05:07.234 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:05:08.121 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:05:08.136 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:05:08.240 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:05:08.240 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:05:08.829 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:05:08.834 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:05:08.835 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:05:08.835 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:05:08.835 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:05:08.835 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:05:08.836 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:05:08.836 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:05:08.836 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:05:08.847 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:05:08.922 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:05:08.923 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:05:08.923 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:05:08.924 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:05:08.924 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:05:08.924 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:05:08.924 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:05:08.924 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:05:08.924 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:05:08.929 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:09:13.161 -03:00 [INF] Application is shutting down...
2025-08-25 16:09:32.255 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:09:32.380 -03:00 [INF] Hosting environment: Production
2025-08-25 16:09:32.382 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:09:33.589 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:09:43.821 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:09:44.662 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:09:44.674 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:09:44.775 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:09:44.775 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:15:52.508 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:15:52.659 -03:00 [INF] Hosting environment: Production
2025-08-25 16:15:52.661 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:15:54.422 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:16:01.880 -03:00 [INF] Application is shutting down...
2025-08-25 16:16:22.361 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:16:22.498 -03:00 [INF] Hosting environment: Production
2025-08-25 16:16:22.500 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:16:23.775 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:16:32.145 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:16:33.001 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:16:33.015 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:16:33.137 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:16:33.137 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:16:33.819 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:16:33.822 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:16:33.823 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:16:33.823 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:16:33.823 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:16:33.823 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:16:33.823 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:16:33.824 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:16:33.824 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:16:33.832 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:16:33.854 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:16:33.854 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:16:33.854 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:16:33.854 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:16:33.854 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:16:33.855 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:16:33.855 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:16:33.855 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:16:33.855 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:16:33.856 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:16:34.754 -03:00 [INF] Application is shutting down...
2025-08-25 16:16:34.795 -03:00 [ERR] Erro ao processar mensagem da API.
System.NullReferenceException: Object reference not set to an instance of an object.
   at Excalibur.ViewModels.MainViewModel.OnPingUpdated(Int64 newPing) in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 591
   at Excalibur.Services.DerivApiService.ProcessMessage(String jsonMessage) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 267
2025-08-25 16:16:52.784 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:16:52.897 -03:00 [INF] Hosting environment: Production
2025-08-25 16:16:52.899 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:16:54.026 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:16:55.656 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:16:56.555 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:16:56.568 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:16:56.644 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:16:56.645 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:16:57.260 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:16:57.264 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:16:57.265 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:16:57.265 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:16:57.265 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:16:57.266 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:16:57.266 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:16:57.266 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:16:57.266 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:16:57.270 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:16:57.270 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:16:57.270 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:16:57.271 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:16:57.271 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:16:57.271 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:16:57.271 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:16:57.271 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:16:57.271 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:16:57.275 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:16:57.275 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:17:10.150 -03:00 [INF] Application is shutting down...
2025-08-25 16:17:29.614 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:17:29.735 -03:00 [INF] Hosting environment: Production
2025-08-25 16:17:29.737 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:17:31.154 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:17:32.641 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:17:33.494 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:17:33.508 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:17:33.607 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:17:33.608 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:17:34.156 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:17:34.159 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:17:34.160 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:17:34.160 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:17:34.160 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:17:34.160 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:17:34.160 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:17:34.160 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:17:34.160 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:17:34.171 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:17:34.184 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:17:34.184 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:17:34.185 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:17:34.185 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:17:34.185 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:17:34.185 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:17:34.185 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:17:34.185 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:17:34.185 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:17:34.185 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:17:42.376 -03:00 [INF] Application is shutting down...
2025-08-25 16:18:04.478 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:18:04.789 -03:00 [INF] Hosting environment: Production
2025-08-25 16:18:04.791 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:18:06.080 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:18:07.443 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:18:08.339 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:18:08.353 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:18:08.436 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:18:08.437 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:18:09.128 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:18:09.134 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:18:09.135 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:18:09.136 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:18:09.136 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:18:09.136 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:18:09.137 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:18:09.137 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:18:09.137 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:18:09.148 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:18:09.205 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:18:09.206 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:18:09.207 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:18:09.207 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:18:09.207 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:18:09.207 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:18:09.207 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:18:09.208 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:18:09.208 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:18:09.209 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:18:18.303 -03:00 [INF] Application is shutting down...
2025-08-25 16:18:33.701 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:18:33.914 -03:00 [INF] Hosting environment: Production
2025-08-25 16:18:33.916 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:18:35.035 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:18:35.818 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:18:36.521 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:18:36.533 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:18:36.624 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:18:36.624 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:18:37.146 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:18:37.149 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:18:37.150 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:18:37.150 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:18:37.150 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:18:37.150 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:18:37.150 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:18:37.150 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:18:37.150 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:18:37.160 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:18:37.180 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:18:37.180 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:18:37.180 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:18:37.180 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:18:37.180 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:18:37.181 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:18:37.181 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:18:37.181 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:18:37.181 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:18:37.181 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:18:50.155 -03:00 [INF] Application is shutting down...
2025-08-25 16:19:13.822 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:19:13.924 -03:00 [INF] Hosting environment: Production
2025-08-25 16:19:13.925 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:19:15.081 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:19:15.977 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:19:16.769 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:19:16.778 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:19:16.858 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:19:16.859 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:19:17.419 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:19:17.424 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:19:17.425 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:19:17.426 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:19:17.426 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:19:17.426 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:19:17.426 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:19:17.426 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:19:17.426 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:19:17.432 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:19:17.432 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:19:17.433 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:19:17.433 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:19:17.433 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:19:17.433 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:19:17.433 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:19:17.433 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:19:17.434 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:19:17.439 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:19:17.440 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:22:14.475 -03:00 [WRN] Conexão perdida: Lost
2025-08-25 16:22:15.562 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-08-25 16:22:16.120 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:22:16.121 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:22:16.680 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:22:16.680 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:22:16.681 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:22:16.681 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:22:16.681 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:22:16.681 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:22:16.681 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:22:16.681 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:22:16.681 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:22:16.682 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:24:20.858 -03:00 [INF] Application is shutting down...
2025-08-25 16:27:22.704 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:27:22.873 -03:00 [INF] Hosting environment: Production
2025-08-25 16:27:22.889 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:27:24.663 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:27:33.420 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:27:34.233 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:27:34.245 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:27:34.337 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:27:34.338 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:27:34.942 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:27:34.947 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:27:34.949 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:27:34.949 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:27:34.949 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:27:34.949 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:27:34.949 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:27:34.949 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:27:34.949 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:27:34.954 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:27:34.954 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:27:34.954 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:27:34.954 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:27:34.955 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:27:34.955 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:27:34.955 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:27:34.955 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:27:34.955 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:27:34.958 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:27:34.959 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:28:00.157 -03:00 [INF] Application is shutting down...
2025-08-25 16:30:49.785 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:30:49.935 -03:00 [INF] Hosting environment: Production
2025-08-25 16:30:49.937 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:30:51.566 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:30:59.625 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:31:00.605 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:31:00.618 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:31:00.715 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:31:00.716 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:31:01.542 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:31:01.546 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:31:01.547 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:31:01.548 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:31:01.548 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:31:01.548 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:31:01.548 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:31:01.548 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:31:01.549 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:31:01.561 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:31:01.617 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:31:01.618 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:31:01.618 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:31:01.619 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:31:01.619 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:31:01.619 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:31:01.619 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:31:01.619 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:31:01.619 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:31:01.620 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:33:24.145 -03:00 [INF] Application is shutting down...
2025-08-25 16:33:36.312 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:33:36.413 -03:00 [INF] Hosting environment: Production
2025-08-25 16:33:36.415 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:33:37.413 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:33:43.782 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:33:44.559 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:33:44.571 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:33:44.631 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:33:44.632 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:33:45.293 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:33:45.297 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:33:45.299 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:33:45.299 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:33:45.299 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:33:45.299 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:33:45.299 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:33:45.300 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:33:45.300 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:33:45.312 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:33:45.330 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:33:45.330 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:33:45.330 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:33:45.330 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:33:45.330 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:33:45.331 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:33:45.331 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:33:45.331 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:33:45.331 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:33:45.331 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:33:50.995 -03:00 [INF] Application is shutting down...
2025-08-25 16:36:27.935 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:36:28.020 -03:00 [INF] Hosting environment: Production
2025-08-25 16:36:28.021 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:36:28.850 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:36:35.064 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:36:35.994 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:36:36.004 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:36:36.059 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:36:36.060 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:36:37.145 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:36:37.148 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:36:37.149 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:36:37.149 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:36:37.149 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:36:37.149 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:36:37.149 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:36:37.149 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:36:37.150 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:36:37.156 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:36:37.341 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:36:37.341 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:36:37.342 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:36:37.342 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:36:37.342 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:36:37.342 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:36:37.342 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:36:37.342 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:36:37.342 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:36:37.344 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:36:47.566 -03:00 [INF] Application is shutting down...
2025-08-25 16:38:52.023 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:38:52.105 -03:00 [INF] Hosting environment: Production
2025-08-25 16:38:52.106 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:38:53.183 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:38:58.501 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:38:59.258 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:38:59.268 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:38:59.327 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:38:59.328 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:38:59.964 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:38:59.967 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:38:59.968 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:38:59.968 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:38:59.968 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:38:59.969 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:38:59.969 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:38:59.969 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:38:59.969 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:38:59.976 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:39:00.025 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:39:00.026 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:39:00.026 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:39:00.026 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:39:00.027 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:39:00.027 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:39:00.027 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:39:00.027 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:39:00.027 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:39:00.027 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:44:54.390 -03:00 [INF] Application is shutting down...
2025-08-25 16:45:25.314 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:45:25.433 -03:00 [INF] Hosting environment: Production
2025-08-25 16:45:25.435 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:45:27.370 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:45:37.869 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:45:38.702 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:45:38.713 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:45:38.805 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:45:38.806 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:45:39.563 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:45:39.567 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:45:39.569 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:45:39.569 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:45:39.569 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:45:39.569 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:45:39.570 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:45:39.570 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:45:39.570 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:45:39.580 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:45:39.674 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:45:39.674 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:45:39.675 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:45:39.675 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:45:39.675 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:45:39.675 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:45:39.675 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:45:39.676 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:45:39.676 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:45:39.676 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:51:37.415 -03:00 [INF] Application is shutting down...
2025-08-25 16:52:22.894 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:52:23.034 -03:00 [INF] Hosting environment: Production
2025-08-25 16:52:23.035 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:52:27.127 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:52:34.668 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:52:35.470 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:52:35.481 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:52:35.572 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:52:35.573 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:52:36.081 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:52:36.086 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:52:36.088 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:52:36.088 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:52:36.089 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:52:36.089 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:52:36.089 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:52:36.089 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:52:36.089 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:52:36.099 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:52:36.136 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:52:36.136 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:52:36.137 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:52:36.137 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:52:36.137 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:52:36.137 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:52:36.137 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:52:36.137 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:52:36.138 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:52:36.138 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:55:47.789 -03:00 [INF] Application is shutting down...
2025-08-25 16:56:13.334 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:56:13.515 -03:00 [INF] Hosting environment: Production
2025-08-25 16:56:13.517 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:56:15.490 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:56:16.477 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:56:17.435 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:56:17.451 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:56:19.542 -03:00 [ERR] [DEBUG] Erro em LoadActiveSymbolsAsync: A requisição para a API Deriv expirou.
System.TimeoutException: A requisição para a API Deriv expirou.
   at Excalibur.Services.DerivApiService.SendRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 312
   at Excalibur.Services.DerivApiService.GetActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.ViewModels.MainViewModel.LoadActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 722
2025-08-25 16:56:19.604 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:56:21.434 -03:00 [WRN] Conexão não responsiva há 4,0s - forçando reconexão URGENTE para Fast Martingale
2025-08-25 16:56:21.626 -03:00 [WRN] Conexão perdida: ByUser
2025-08-25 16:56:23.134 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:56:23.546 -03:00 [ERR] Erro ao processar mensagem da API.
System.Collections.Generic.KeyNotFoundException: The given key was not present in the dictionary.
   at System.Text.Json.JsonElement.GetProperty(String propertyName)
   at Excalibur.Services.DerivApiService.ProcessMessage(String jsonMessage) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 257
2025-08-25 16:56:23.550 -03:00 [ERR] Erro ao processar mensagem da API.
System.Collections.Generic.KeyNotFoundException: The given key was not present in the dictionary.
   at System.Text.Json.JsonElement.GetProperty(String propertyName)
   at Excalibur.Services.DerivApiService.ProcessMessage(String jsonMessage) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 257
2025-08-25 16:56:23.735 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:56:23.736 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:56:23.767 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:56:23.767 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:56:25.738 -03:00 [ERR] [DEBUG] Erro em LoadActiveSymbolsAsync: A requisição para a API Deriv expirou.
System.TimeoutException: A requisição para a API Deriv expirou.
   at Excalibur.Services.DerivApiService.SendRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 312
   at Excalibur.Services.DerivApiService.GetActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.ViewModels.MainViewModel.LoadActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 722
2025-08-25 16:56:25.740 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:56:25.769 -03:00 [ERR] [DEBUG] Erro em LoadActiveSymbolsAsync: A requisição para a API Deriv expirou.
System.TimeoutException: A requisição para a API Deriv expirou.
   at Excalibur.Services.DerivApiService.SendRequestAsync[T](T request) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 312
   at Excalibur.Services.DerivApiService.GetActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 351
   at Excalibur.ViewModels.MainViewModel.LoadActiveSymbolsAsync() in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 722
2025-08-25 16:56:25.771 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:56:26.441 -03:00 [ERR] Erro ao processar mensagem da API.
System.Collections.Generic.KeyNotFoundException: The given key was not present in the dictionary.
   at System.Text.Json.JsonElement.GetProperty(String propertyName)
   at Excalibur.Services.DerivApiService.ProcessMessage(String jsonMessage) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 257
2025-08-25 16:56:26.441 -03:00 [ERR] Erro ao processar mensagem da API.
System.Collections.Generic.KeyNotFoundException: The given key was not present in the dictionary.
   at System.Text.Json.JsonElement.GetProperty(String propertyName)
   at Excalibur.Services.DerivApiService.ProcessMessage(String jsonMessage) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 257
2025-08-25 16:56:26.572 -03:00 [WRN] Conexão perdida: ByServer
2025-08-25 16:56:26.584 -03:00 [WRN] Conexão perdida: Lost
2025-08-25 16:56:27.757 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-08-25 16:56:28.353 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:56:28.354 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:56:29.092 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:56:29.097 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:56:29.098 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:56:29.098 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:56:29.099 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:56:29.099 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:56:29.099 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:56:29.099 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:56:29.099 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:56:29.101 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:57:31.358 -03:00 [INF] Application is shutting down...
2025-08-25 16:59:39.548 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 16:59:39.662 -03:00 [INF] Hosting environment: Production
2025-08-25 16:59:39.663 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 16:59:40.708 -03:00 [INF] Conectando à API Deriv...
2025-08-25 16:59:46.756 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 16:59:47.541 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:59:47.552 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:59:47.647 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 16:59:47.647 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 16:59:48.228 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:59:48.230 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:59:48.231 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:59:48.231 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:59:48.231 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:59:48.232 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:59:48.232 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:59:48.232 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:59:48.232 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:59:48.235 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 16:59:48.236 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 16:59:48.236 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 16:59:48.236 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 16:59:48.236 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 16:59:48.236 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 16:59:48.236 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 16:59:48.236 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 16:59:48.237 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 16:59:48.239 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 16:59:48.240 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 17:08:04.578 -03:00 [INF] Application is shutting down...
2025-08-25 17:08:24.083 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-25 17:08:24.183 -03:00 [INF] Hosting environment: Production
2025-08-25 17:08:24.185 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.3
2025-08-25 17:08:25.320 -03:00 [INF] Conectando à API Deriv...
2025-08-25 17:08:30.598 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 17:08:31.420 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 17:08:31.429 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 17:08:31.487 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 17:08:31.488 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 17:08:32.356 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 17:08:32.359 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 17:08:32.360 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 17:08:32.360 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 17:08:32.360 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 17:08:32.360 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 17:08:32.360 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 17:08:32.360 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 17:08:32.360 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 17:08:32.368 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 17:08:32.401 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 17:08:32.401 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 17:08:32.401 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 17:08:32.401 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 17:08:32.402 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 17:08:32.402 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 17:08:32.402 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 17:08:32.402 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 17:08:32.402 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 17:08:32.402 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-08-25 17:09:27.391 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 17:09:27.394 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 17:09:27.425 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 17:10:05.749 -03:00 [WRN] Conexão perdida: Lost
2025-08-25 17:10:06.865 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-08-25 17:10:07.499 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 17:10:07.499 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 17:10:08.262 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 17:10:08.262 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 17:10:08.283 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-08-25 17:10:08.287 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 17:10:08.287 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 17:10:08.287 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 17:10:08.288 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 17:10:08.288 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 17:10:08.288 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 17:10:08.288 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 17:10:08.696 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 1/10
2025-08-25 17:10:08.796 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 2/10
2025-08-25 17:10:08.896 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 3/10
2025-08-25 17:10:08.996 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 4/10
2025-08-25 17:10:09.098 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 17:10:09.098 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 17:10:09.099 -03:00 [INF] [DEBUG] Tipo de contrato restaurado: ASIAND
2025-08-25 17:10:09.099 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 17:10:09.099 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=ASIAND
2025-08-25 17:11:56.447 -03:00 [WRN] Conexão perdida: Lost
2025-08-25 17:11:57.458 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-08-25 17:11:58.022 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 17:11:58.023 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 17:11:58.569 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 17:11:58.569 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 17:11:58.575 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-08-25 17:11:58.577 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 17:11:58.578 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 17:11:58.578 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 17:11:58.578 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 17:11:58.578 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 17:11:58.578 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 17:11:58.578 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 17:11:58.987 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 1/10
2025-08-25 17:11:59.087 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 2/10
2025-08-25 17:11:59.187 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 3/10
2025-08-25 17:11:59.288 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 4/10
2025-08-25 17:11:59.389 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 17:11:59.389 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 17:11:59.390 -03:00 [INF] [DEBUG] Tipo de contrato restaurado: ASIAND
2025-08-25 17:11:59.390 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=ASIAND
2025-08-25 17:11:59.390 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 17:19:44.022 -03:00 [WRN] Conexão não responsiva há 3,2s - forçando reconexão URGENTE para Fast Martingale
2025-08-25 17:19:45.918 -03:00 [WRN] Conexão perdida: ByUser
2025-08-25 17:19:47.463 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-08-25 17:19:47.935 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 17:19:47.936 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 17:19:48.459 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 17:19:48.459 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 17:19:48.464 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-08-25 17:19:48.468 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 17:19:48.468 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 17:19:48.469 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 17:19:48.469 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 17:19:48.469 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 17:19:48.469 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 17:19:48.469 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 17:19:48.876 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 1/10
2025-08-25 17:19:48.977 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 2/10
2025-08-25 17:19:49.078 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 3/10
2025-08-25 17:19:49.178 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 4/10
2025-08-25 17:19:49.280 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 17:19:49.280 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 17:19:49.280 -03:00 [INF] [DEBUG] Tipo de contrato restaurado: ASIAND
2025-08-25 17:19:49.280 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=ASIAND
2025-08-25 17:19:49.280 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 17:23:31.310 -03:00 [WRN] Conexão perdida: Lost
2025-08-25 17:23:32.280 -03:00 [INF] Reconexão bem-sucedida: Lost
2025-08-25 17:23:32.758 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-08-25 17:23:32.758 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-08-25 17:23:33.275 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-08-25 17:23:33.275 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-08-25 17:23:33.279 -03:00 [INF] [TICKS] Unsubscribed from ticks for symbol: R_10
2025-08-25 17:23:33.281 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-08-25 17:23:33.282 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-08-25 17:23:33.282 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-08-25 17:23:33.282 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-08-25 17:23:33.282 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-08-25 17:23:33.282 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-08-25 17:23:33.282 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-08-25 17:23:33.686 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 1/10
2025-08-25 17:23:33.787 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 2/10
2025-08-25 17:23:33.887 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 3/10
2025-08-25 17:23:33.988 -03:00 [INF] [DEBUG] Lista de contratos ainda vazia, tentativa 4/10
2025-08-25 17:23:34.089 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-08-25 17:23:34.089 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=ASIAND, Symbol=R_10, Stake=100
2025-08-25 17:23:34.089 -03:00 [INF] [DEBUG] Tipo de contrato restaurado: ASIAND
2025-08-25 17:23:34.089 -03:00 [INF] [DEBUG] Seleções restauradas: Market=Derived, SubMarket=Continuous Indices, Symbol=R_10, ContractType=ASIAND
2025-08-25 17:23:34.089 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-08-25 17:24:59.372 -03:00 [INF] Application is shutting down...
2025-08-25 17:24:59.394 -03:00 [ERR] [TICKS] Error processing tick update
System.NullReferenceException: Object reference not set to an instance of an object.
   at Excalibur.ViewModels.MainViewModel.OnTickReceived(Decimal price, DateTime timestamp) in C:\Users\<USER>\Downloads\excalibur1.3\ViewModels\MainViewModel.cs:line 659
   at Excalibur.Services.DerivApiService.ProcessTickUpdate(JsonElement root) in C:\Users\<USER>\Downloads\excalibur1.3\Services\DerivApiService.cs:line 975
