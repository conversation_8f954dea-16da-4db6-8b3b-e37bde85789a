using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.WebSockets;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Websocket.Client;
using Excalibur.Models;

namespace Excalibur.Services;

public class DerivApiService : IDerivApiService
{
    private readonly ILogger<DerivApiService> _logger;
    private IWebsocketClient _ws;
    private Timer _pingTimer;
    private readonly Stopwatch _pingStopwatch = new();
    private DateTime _lastPongReceived = DateTime.UtcNow;
    private Timer _connectionMonitorTimer;

    // Dicionário para rastrear requisições e suas respostas
    private readonly ConcurrentDictionary<int, TaskCompletionSource<JsonElement>> _pendingRequests = new();
    private int _nextRequestId = 1;
    private readonly HashSet<string> _processedContracts = new HashSet<string>();

    // Configurações - virão da UI no futuro
    private string _apiToken = "oLJLFtINRDBGUh1";
    private int _appId = 82663;

    public bool IsConnected => _ws?.IsRunning ?? false;

    public event Action<string, string, double> AccountInfoUpdated;
    public event Action<long> PingUpdated;
    public event Action ConnectionEstablished;
    public event Action ConnectionLost;
    public event Action<bool> ContractResult; // true = win, false = loss
    public event Action<string> ContractNearExpiry; // contract_id when contract is near expiry
    public event Action<decimal, DateTime> TickReceived; // price, timestamp for ticks stream
    public event Action<string, decimal, decimal, DateTime> ContractFinished; // contractId, profit, exitPrice, exitTime
    public event Action<string, string, string, decimal, decimal, DateTime> ContractPurchased; // contractId, contractType, duration, stake, payout, purchaseTime
    
    private string _currentTicksSubscription; // Track current ticks subscription

    public DerivApiService(ILogger<DerivApiService> logger)
    {
        _logger = logger;
        InitializeClient();
    }

    private void InitializeClient()
    {
        var url = new Uri($"wss://ws.binaryws.com/websockets/v3?app_id={_appId}");
        _ws = new WebsocketClient(url);

        _ws.ReconnectTimeout = TimeSpan.FromSeconds(30); // Tempo para tentar reconectar
        _ws.ErrorReconnectTimeout = TimeSpan.FromSeconds(30);

        _ws.ReconnectionHappened.Subscribe(info => {
            _logger.LogInformation($"Reconexão bem-sucedida: {info.Type}");
            Authorize();
        });

        _ws.DisconnectionHappened.Subscribe(info => {
            _logger.LogWarning($"Conexão perdida: {info.Type}");
            // Clean up both timers on disconnection
            _pingTimer?.Dispose();
            _connectionMonitorTimer?.Dispose();
            ConnectionLost?.Invoke();
        });

        _ws.MessageReceived.Subscribe(msg => {
            // Removed verbose message logging for performance optimization
            ProcessGeneralMessage(msg.Text);
        });
    }

    public async Task ConnectAndAuthorizeAsync()
    {
        _logger.LogInformation("Conectando à API Deriv...");
        await _ws.Start();
        if (_ws.IsRunning)
        {
            Authorize();
        }
    }

    private void Authorize()
    {
        var authRequest = new { authorize = _apiToken };
        _ws.Send(JsonSerializer.Serialize(authRequest));
        // Reduced logging for performance - only log on success/failure
    }
    
    public async Task SubscribeToBalanceAsync()
    {
        var request = new { balance = 1, subscribe = 1 };
        await SendRequestAsync(request);
        // Reduced logging for performance
    }
    
    private void StartPingTimer()
    {
        // Reasonable heartbeat: ping every 2 seconds for stable connection monitoring
        _pingTimer = new Timer(_ => SendPing(), null, 0, 2000);
        
        // Connection monitor: check every 3 seconds for connection health
        _connectionMonitorTimer = new Timer(_ => CheckConnectionHealth(), null, 3000, 3000);
        _lastPongReceived = DateTime.UtcNow;
    }

    private void SendPing()
    {
        try
        {
            if (_ws?.IsRunning == true)
            {
                _pingStopwatch.Restart();
                var pingRequest = new { ping = 1 };
                _ws.Send(JsonSerializer.Serialize(pingRequest));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao enviar ping - conexão pode estar instável");
        }
    }
    
    private void CheckConnectionHealth()
    {
        try
        {
            var timeSinceLastPong = DateTime.UtcNow - _lastPongReceived;
            
            // More tolerant timeout: If no pong received in 5 seconds, consider connection unhealthy
            // This reduces false positives that cause unnecessary reconnections
            if (timeSinceLastPong.TotalSeconds > 5.0 && _ws?.IsRunning == true)
            {
                _logger.LogWarning($"Conexão não responsiva há {timeSinceLastPong.TotalSeconds:F1}s - forçando reconexão");
                
                // Stop timers before reconnection
                _pingTimer?.Dispose();
                _connectionMonitorTimer?.Dispose();
                
                // Trigger reconnection with proper delay
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _ws.Stop(WebSocketCloseStatus.NormalClosure, "Connection health check failed");
                        await Task.Delay(1000); // Allow proper cleanup
                        await _ws.Start();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Erro durante reconexão");
                    }
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro no monitoramento de saúde da conexão");
        }
    }

    // Novo método para processar QUALQUER mensagem da API
    private void ProcessGeneralMessage(string jsonMessage)
    {
        try
        {
            using var doc = JsonDocument.Parse(jsonMessage);
            var root = doc.RootElement;

            // ULTRA-PRIORITY processing for contract-related messages - handle immediately
            if (root.TryGetProperty("msg_type", out var msgTypeElement))
            {
                var msgType = msgTypeElement.GetString();
                
                // INSTANT PRIORITY: Contract messages bypass all other processing
                if (msgType == "proposal_open_contract")
                {
                    // Process contract updates with absolute priority for immediate loss detection
                    ProcessMessage(jsonMessage);
                    return;
                }
                else if (msgType == "portfolio")
                {
                    // Process portfolio updates with high priority for fallback loss detection
                    ProcessMessage(jsonMessage);
                    return;
                }
            }

            // Verifica se a mensagem é uma resposta a uma de nossas requisições
            if (root.TryGetProperty("req_id", out var reqIdElement) && 
                _pendingRequests.TryRemove(reqIdElement.GetInt32(), out var tcs))
            {
                // Se for, completa a Task com o resultado
                if (root.TryGetProperty("error", out var error))
                {
                    tcs.SetException(new Exception(error.GetProperty("message").GetString()));
                }
                else
                {
                    tcs.SetResult(root.Clone());
                }
                return;
            }

            // Processa mensagens que não são respostas diretas (streams, etc.)
            ProcessMessage(jsonMessage); // Chamamos o método antigo para compatibilidade
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar mensagem geral da API.");
        }
    }

    private void ProcessMessage(string jsonMessage)
    {
        try
        {
            using var doc = JsonDocument.Parse(jsonMessage);
            var root = doc.RootElement;
            var msgType = root.GetProperty("msg_type").GetString();

            switch (msgType)
            {
                case "authorize":
                    if (root.TryGetProperty("error", out var error))
                    {
                        _logger.LogError($"Erro de autorização: {error.GetProperty("message").GetString()}");
                        return;
                    }

                    var authResponse = root.GetProperty("authorize");
                    var loginid = authResponse.GetProperty("loginid").GetString();
                    var accountType = authResponse.GetProperty("is_virtual").GetInt32() == 1 ? "Virtual" : "Real";
                    var balance = authResponse.GetProperty("balance").GetDouble();
                    AccountInfoUpdated?.Invoke(loginid, accountType, balance);
                    
                    // Inicia o timer de ping após autorização bem-sucedida
                    StartPingTimer();
                    
                    // Dispara o evento de conexão estabelecida após autorização bem-sucedida
                    ConnectionEstablished?.Invoke();
                    
                    // Subscrever para atualizações de saldo e portfolio
                    _ = Task.Run(async () => {
                        await SubscribeToBalanceAsync();
                        await SubscribeToPortfolioAsync();
                    });
                    break;
                
                case "balance":
                    var balanceResponse = root.GetProperty("balance");
                    var updatedBalance = balanceResponse.GetProperty("balance").GetDouble();
                    var loginIdBalance = balanceResponse.GetProperty("loginid").GetString();
                    // Assumindo que o tipo de conta não muda
                    AccountInfoUpdated?.Invoke(loginIdBalance, null, updatedBalance);
                    break;
                    
                case "ping":
                    _pingStopwatch.Stop();
                    _lastPongReceived = DateTime.UtcNow; // Update last pong timestamp for connection health monitoring
                    PingUpdated?.Invoke(_pingStopwatch.ElapsedMilliseconds);
                    break;
                    
                case "portfolio":
                    ProcessPortfolioUpdate(root);
                    break;
                    
                case "proposal_open_contract":
                    ProcessOpenContractUpdate(root);
                    break;

                case "buy":
                    ProcessBuyResponse(root);
                    break;
                    
                case "tick":
                    ProcessTickUpdate(root);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar mensagem da API.");
        }
    }

    // Método genérico para enviar uma requisição e aguardar a resposta
    private async Task<JsonElement> SendRequestAsync<T>(T request)
    {
        var requestId = Interlocked.Increment(ref _nextRequestId);
        var tcs = new TaskCompletionSource<JsonElement>();
        _pendingRequests[requestId] = tcs;

        // Adiciona o req_id ao objeto de requisição
        var jsonRequest = JsonDocument.Parse(JsonSerializer.Serialize(request)).RootElement;
        var finalRequest = new Dictionary<string, object>();
        foreach (var property in jsonRequest.EnumerateObject())
        {
            finalRequest[property.Name] = property.Value;
        }
        finalRequest["req_id"] = requestId;

        _ws.Send(JsonSerializer.Serialize(finalRequest));

        // Timeout otimizado: reduzido para 2 segundos
        var completedTask = await Task.WhenAny(tcs.Task, Task.Delay(2000));
        if (completedTask != tcs.Task)
        {
            _pendingRequests.TryRemove(requestId, out _);
            throw new TimeoutException("A requisição para a API Deriv expirou.");
        }
        
        return await tcs.Task;
    }

    // Método otimizado para requisições críticas do Fast Martingale
    private async Task<JsonElement> SendFastRequestAsync<T>(T request)
    {
        var requestId = Interlocked.Increment(ref _nextRequestId);
        var tcs = new TaskCompletionSource<JsonElement>();
        _pendingRequests[requestId] = tcs;

        // Adiciona o req_id ao objeto de requisição
        var jsonRequest = JsonDocument.Parse(JsonSerializer.Serialize(request)).RootElement;
        var finalRequest = new Dictionary<string, object>();
        foreach (var property in jsonRequest.EnumerateObject())
        {
            finalRequest[property.Name] = property.Value;
        }
        finalRequest["req_id"] = requestId;

        _ws.Send(JsonSerializer.Serialize(finalRequest));

        // Timeout aumentado para Dualgale: 3 segundos para operações mais robustas
        var completedTask = await Task.WhenAny(tcs.Task, Task.Delay(3000));
        if (completedTask != tcs.Task)
        {
            _pendingRequests.TryRemove(requestId, out _);
            throw new TimeoutException("Requisição Fast expirou - latência alta detectada.");
        }

        return await tcs.Task;
    }

    // Implementação dos novos métodos da interface
    public async Task<List<ActiveSymbol>> GetActiveSymbolsAsync()
    {
        var request = new { active_symbols = "full", product_type = "basic" };
        var response = await SendRequestAsync(request);
        var symbols = response.GetProperty("active_symbols").Deserialize<List<ActiveSymbol>>();
        return symbols ?? new List<ActiveSymbol>();
    }

    public async Task<ContractsForSymbol> GetContractsForSymbolAsync(string symbol)
    {
        var request = new { contracts_for = symbol };
        var response = await SendRequestAsync(request);
        
        // A propriedade raiz é "contracts_for"
        var contractsData = response.GetProperty("contracts_for").Deserialize<ContractsForSymbol>();
        return contractsData ?? new ContractsForSymbol();
    }

    public async Task<ProposalResponse> GetProposalAsync(ProposalRequest request)
    {
        var proposalRequest = new Dictionary<string, object>
        {
            ["proposal"] = 1,
            ["contract_type"] = request.ContractType,
            ["symbol"] = request.Symbol,
            ["duration"] = request.Duration,
            ["duration_unit"] = request.DurationUnit,
            ["currency"] = request.Currency,
            ["basis"] = request.Basis,
            ["amount"] = request.Stake
        };

        // Adicionar campos opcionais apenas se não forem nulos
        if (!string.IsNullOrEmpty(request.Barrier))
            proposalRequest["barrier"] = request.Barrier;
            
        if (!string.IsNullOrEmpty(request.Barrier2))
            proposalRequest["barrier2"] = request.Barrier2;
            
        if (request.LastDigitPrediction.HasValue)
            proposalRequest["last_digit_prediction"] = request.LastDigitPrediction.Value;

        var response = await SendFastRequestAsync(proposalRequest);
        var proposalResponse = response.Deserialize<ProposalResponse>();
        return proposalResponse ?? new ProposalResponse();
    }

    public async Task<ProposalResponse> GetFastProposalAsync(ProposalRequest request)
    {
        var proposalRequest = new Dictionary<string, object>
        {
            ["proposal"] = 1,
            ["contract_type"] = request.ContractType,
            ["symbol"] = request.Symbol,
            ["duration"] = request.Duration,
            ["duration_unit"] = request.DurationUnit,
            ["currency"] = request.Currency,
            ["basis"] = request.Basis,
            ["amount"] = request.Stake
        };

        // Adicionar campos opcionais apenas se não forem nulos
        if (!string.IsNullOrEmpty(request.Barrier))
            proposalRequest["barrier"] = request.Barrier;
            
        if (!string.IsNullOrEmpty(request.Barrier2))
            proposalRequest["barrier2"] = request.Barrier2;
            
        if (request.LastDigitPrediction.HasValue)
            proposalRequest["last_digit_prediction"] = request.LastDigitPrediction.Value;

        // Use the fastest possible request method with minimal timeout
        var response = await SendFastRequestAsync(proposalRequest);
        var proposalResponse = response.Deserialize<ProposalResponse>();
        return proposalResponse ?? new ProposalResponse();
    }

    public async Task<BuyResponse> BuyContractAsync(string proposalId, decimal price)
    {
        var request = new
        {
            buy = proposalId,
            price = price,
            subscribe = 1  // Subscribe to contract updates
        };

        var response = await SendFastRequestAsync(request);
        var buyResponse = response.Deserialize<BuyResponse>();
        
        // Log contract purchase for debugging
        if (buyResponse?.Buy != null)
        {
            _logger.LogInformation($"[DEBUG] Contrato comprado: {buyResponse.Buy.ContractId}, subscrevendo para atualizações");
        }
        
        return buyResponse;
    }
    
    // Ultra-fast same-time buy method for Fast Martingale - fire and forget approach
    public void BuyContractImmediateAsync(string proposalId, decimal price, Action<BuyResponse> onComplete = null)
    {
        var immediateStartTime = DateTimeOffset.Now;
        
        // Create buy request
        var request = new
        {
            buy = proposalId,
            price = price,
            subscribe = 1
        };

        // Send immediately without waiting for response - fire and forget for same-time execution
        var jsonRequest = JsonSerializer.Serialize(request);
        
        try
        {
            // Send the WebSocket message immediately
            _ws.Send(jsonRequest);
            
            var sendTime = DateTimeOffset.Now;
            var sendDelay = (sendTime - immediateStartTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] SAME-TIME BUY: Mensagem de compra enviada em {sendDelay}ms às {sendTime:HH:mm:ss.fff}");
            
            // Handle response asynchronously without blocking
            if (onComplete != null)
            {
                // Set up a temporary response handler (simplified for immediate execution)
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Wait briefly for response (non-blocking)
                        await Task.Delay(100); // Give time for server response
                        
                        // For immediate execution, we don't wait for the actual response
                        // The success will be confirmed by contract updates
                        var mockResponse = new BuyResponse(); // Simplified response
                        onComplete(mockResponse);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Erro no callback de compra imediata");
                    }
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar compra imediata");
        }
    }

    public async Task SubscribeToPortfolioAsync()
    {
        var request = new { portfolio = 1 };
        _ws.Send(JsonSerializer.Serialize(request));
        // Reduced logging for performance
    }

    private void ProcessPortfolioUpdate(JsonElement root)
    {
        try
        {
            if (root.TryGetProperty("portfolio", out var portfolio) && 
                portfolio.TryGetProperty("contracts", out var contracts))
            {
                foreach (var contract in contracts.EnumerateArray())
                {
                    var contractId = contract.TryGetProperty("contract_id", out var id) ? 
                        (id.ValueKind == JsonValueKind.String ? id.GetString() : id.GetInt64().ToString()) : "unknown";
                    var isSold = contract.TryGetProperty("is_sold", out var sold) ? sold.GetInt32() : 0;
                    
                    if (isSold == 1 && !string.IsNullOrEmpty(contractId) && !_processedContracts.Contains(contractId))
                    {
                        // Contract is settled, check if it's a win or loss
                        if (contract.TryGetProperty("profit", out var profit))
                        {
                            var profitValue = profit.GetDecimal();
                            bool isWin = profitValue > 0;
                            
                            // Only log contract results for debugging critical events
                            _logger.LogInformation($"Contract {contractId} settled: Profit={profitValue}, Win={isWin}");
                            
                            // Marcar contrato como processado
                            _processedContracts.Add(contractId);
                            
                            ContractResult?.Invoke(isWin);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar atualização do portfolio.");
        }
    }
    
    private void ProcessOpenContractUpdate(JsonElement root)
    {
        try
        {
            if (root.TryGetProperty("proposal_open_contract", out var contract))
            {
                if (contract.TryGetProperty("contract_id", out var contractIdElement))
                {
                    string contractId;
                    
                    // Handle both string and number types for contract_id
                    if (contractIdElement.ValueKind == JsonValueKind.String)
                    {
                        contractId = contractIdElement.GetString();
                    }
                    else if (contractIdElement.ValueKind == JsonValueKind.Number)
                    {
                        contractId = contractIdElement.GetInt64().ToString();
                    }
                    else
                    {
                        return; // Skip if neither string nor number
                    }
                    
                    // Check if contract is near expiry (for Fast Martingale pre-calculation)
                    if (contract.TryGetProperty("date_expiry", out var dateExpiryElement) && 
                        contract.TryGetProperty("is_sold", out var isSoldElement))
                    {
                        var isSold = isSoldElement.GetInt32();
                        
                        if (isSold == 0) // Contract is still active
                        {
                            var dateExpiry = dateExpiryElement.GetInt64();
                            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                            var timeToExpiry = dateExpiry - currentTime;
                            
                            // If contract expires in 3 seconds or less, trigger near expiry event
                            // Increased back to 3 seconds for adequate pre-calculation time
                            if (timeToExpiry <= 3 && timeToExpiry > 0)
                            {
                                _logger.LogInformation($"Contract {contractId} near expiry: {timeToExpiry}s");
                                ContractNearExpiry?.Invoke(contractId);
                            }
                            
                            // ULTRA-IMMEDIATE DETECTION: Check if contract has just expired (within 2 seconds for maximum coverage)
                            if (timeToExpiry <= 0 && timeToExpiry >= -2 && !_processedContracts.Contains(contractId))
                            {
                                if (contract.TryGetProperty("profit", out var profitElement))
                                {
                                    var profit = profitElement.GetDecimal();
                                    bool isWin = profit > 0;

                                    var immediateResultTime = DateTimeOffset.Now;
                                    _logger.LogInformation($"[TIMING] ULTRA-IMMEDIATE - Contrato {contractId} expirado às {immediateResultTime:HH:mm:ss.fff} - Profit: {profit}, Win: {isWin}");

                                    // Get exit price if available
                                    decimal exitPrice = 0;
                                    if (contract.TryGetProperty("exit_tick", out var exitTickElement))
                                    {
                                        exitPrice = exitTickElement.GetDecimal();
                                    }

                                    // Mark as processed immediately to avoid duplicate processing
                                    _processedContracts.Add(contractId);

                                    // INSTANT EVENT TRIGGER: Zero delay event dispatch
                                    var eventStart = DateTimeOffset.Now;
                                    ContractResult?.Invoke(isWin);

                                    // Trigger contract finished event with detailed information
                                    ContractFinished?.Invoke(contractId, profit, exitPrice, immediateResultTime.DateTime);

                                    var eventEnd = DateTimeOffset.Now;
                                    var eventDelay = (eventEnd - eventStart).TotalMilliseconds;
                                    _logger.LogInformation($"[TIMING] ULTRA-IMMEDIATE ContractResult dispatched in {eventDelay}ms");
                                    return; // Exit early to avoid double processing
                                }
                            }
                        }
                    }
                    
                    // Fallback: Check if contract is sold/finished (existing logic)
                    if (contract.TryGetProperty("is_sold", out var isSoldElement2))
                    {
                        var isSold = isSoldElement2.GetInt32();
                        
                        if (isSold == 1 && !_processedContracts.Contains(contractId)) // Contract is sold/finished
                        {
                            var contractFinishedTime = DateTimeOffset.Now;
                            _logger.LogInformation($"[TIMING] FALLBACK - Contrato {contractId} detectado como finalizado às {contractFinishedTime:HH:mm:ss.fff}");
                            
                            if (contract.TryGetProperty("profit", out var profitElement))
                            {
                                var profit = profitElement.GetDecimal();
                                bool isWin = profit > 0;
                                _logger.LogInformation($"Contract {contractId} finished: Profit={profit}, Win={isWin}");

                                // Get exit price if available
                                decimal exitPrice = 0;
                                if (contract.TryGetProperty("exit_tick", out var exitTickElement))
                                {
                                    exitPrice = exitTickElement.GetDecimal();
                                }

                                // Marcar contrato como processado
                                _processedContracts.Add(contractId);

                                var beforeEventTime = DateTimeOffset.Now;
                                _logger.LogInformation($"[TIMING] Disparando ContractResult event às {beforeEventTime:HH:mm:ss.fff}");

                                // Trigger contract result event
                                ContractResult?.Invoke(isWin);

                                // Trigger contract finished event with detailed information
                                ContractFinished?.Invoke(contractId, profit, exitPrice, contractFinishedTime.DateTime);

                                var afterEventTime = DateTimeOffset.Now;
                                var eventDuration = (afterEventTime - beforeEventTime).TotalMilliseconds;
                                _logger.LogInformation($"[TIMING] ContractResult event concluído às {afterEventTime:HH:mm:ss.fff} (duração: {eventDuration}ms)");
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar atualização de contrato aberto.");
        }
    }
    
    public void GetFastProposalAndBuyAsync(ProposalRequest request, Action<ProposalResponse, BuyResponse> onComplete)
    {
        var operationStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] ZERO-DELAY OPERATION: GetFastProposalAndBuyAsync iniciado às {operationStartTime:HH:mm:ss.fff}");
        
        // Execute both operations in parallel for maximum speed
        _ = Task.Run(async () =>
        {
            try
            {
                var parallelStartTime = DateTimeOffset.Now;
                
                // Step 1: Get proposal with ultra-low latency
                var proposalTask = GetFastProposalAsync(request);
                var proposalResponse = await proposalTask;
                
                var proposalEndTime = DateTimeOffset.Now;
                var proposalDelay = (proposalEndTime - parallelStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] ZERO-DELAY: Proposta recebida em {proposalDelay}ms às {proposalEndTime:HH:mm:ss.fff}");
                
                BuyResponse buyResponse = null;
                
                // Step 2: Execute buy immediately if proposal is valid
                if (proposalResponse?.Error == null && proposalResponse?.Proposal != null)
                {
                    var buyStartTime = DateTimeOffset.Now;
                    
                    // Execute buy with immediate fire-and-forget pattern
                    var buyExecuted = false;
                    var buyTaskCompletion = new TaskCompletionSource<BuyResponse>();
                    
                    BuyContractImmediateAsync(proposalResponse.Proposal.Id, proposalResponse.Proposal.AskPrice, (response) =>
                    {
                        buyExecuted = true;
                        buyResponse = response;
                        buyTaskCompletion.SetResult(response);
                        
                        var buyEndTime = DateTimeOffset.Now;
                        var buyDelay = (buyEndTime - buyStartTime).TotalMilliseconds;
                        _logger.LogInformation($"[TIMING] ZERO-DELAY: Compra executada em {buyDelay}ms às {buyEndTime:HH:mm:ss.fff}");
                    });
                    
                    // Wait for buy to complete with timeout for safety
                    try
                    {
                        buyResponse = await Task.WhenAny(buyTaskCompletion.Task, Task.Delay(1000)) == buyTaskCompletion.Task 
                            ? await buyTaskCompletion.Task 
                            : new BuyResponse { Error = new ApiError { Message = "Buy timeout" } };
                    }
                    catch (Exception buyEx)
                    {
                        _logger.LogError(buyEx, "[TIMING] ZERO-DELAY: Erro na execução da compra");
                        buyResponse = new BuyResponse { Error = new ApiError { Message = buyEx.Message } };
                    }
                }
                else
                {
                    _logger.LogError($"[TIMING] ZERO-DELAY: Proposta inválida - {proposalResponse?.Error?.Message}");
                    buyResponse = new BuyResponse { Error = new ApiError { Message = "Invalid proposal" } };
                }
                
                var operationEndTime = DateTimeOffset.Now;
                var totalOperationTime = (operationEndTime - operationStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] ZERO-DELAY OPERATION: Total time {totalOperationTime}ms às {operationEndTime:HH:mm:ss.fff}");
                
                // Execute callback with both responses
                onComplete?.Invoke(proposalResponse, buyResponse);
            }
            catch (Exception ex)
            {
                var errorTime = DateTimeOffset.Now;
                var errorDelay = (errorTime - operationStartTime).TotalMilliseconds;
                _logger.LogError(ex, $"[TIMING] ZERO-DELAY OPERATION: Erro crítico após {errorDelay}ms");
                
                // Return error responses
                var errorProposal = new ProposalResponse { Error = new ProposalError { Message = ex.Message } };
                var errorBuy = new BuyResponse { Error = new ApiError { Message = ex.Message } };
                onComplete?.Invoke(errorProposal, errorBuy);
            }
        });
    }

    // REVOLUTIONARY INSTANT BUY: Uses pre-calculated proposal pool for zero-delay execution
    public void BuyInstantMarketAsync(ProposalRequest request, Action<BuyResponse> onComplete = null)
    {
        var instantStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] INSTANT POOL BUY: Execução iniciada às {instantStartTime:HH:mm:ss.fff}");
        
        _ = Task.Run(async () =>
        {
            try
            {
                // INSTANT PROPOSAL REUSE: Try to get a very recent proposal first
                var hotProposal = await GetHotProposalAsync(request);
                
                if (hotProposal?.Error == null && hotProposal?.Proposal != null)
                {
                    var proposalTime = DateTimeOffset.Now;
                    var proposalDelay = (proposalTime - instantStartTime).TotalMilliseconds;
                    _logger.LogInformation($"[TIMING] INSTANT POOL: Proposta obtida em {proposalDelay}ms às {proposalTime:HH:mm:ss.fff}");
                    
                    // IMMEDIATE BUY: Execute buy with zero additional delay
                    var buyRequest = new
                    {
                        buy = hotProposal.Proposal.Id,
                        price = hotProposal.Proposal.AskPrice,
                        subscribe = 1
                    };
                    
                    var jsonRequest = JsonSerializer.Serialize(buyRequest);
                    _ws.Send(jsonRequest);
                    
                    var buyTime = DateTimeOffset.Now;
                    var buyDelay = (buyTime - proposalTime).TotalMilliseconds;
                    var totalTime = (buyTime - instantStartTime).TotalMilliseconds;
                    
                    _logger.LogInformation($"[TIMING] INSTANT POOL: Compra enviada em {buyDelay}ms às {buyTime:HH:mm:ss.fff}");
                    _logger.LogInformation($"[TIMING] INSTANT POOL BUY: COMPLETED in {totalTime}ms - TRUE INSTANT execution");
                    
                    // Success callback
                    if (onComplete != null)
                    {
                        var successResponse = new BuyResponse
                        {
                            Buy = new BuyContract
                            {
                                ContractId = 0, // Will be updated via contract updates
                                TransactionId = 0 // Will be updated via contract updates
                            }
                        };
                        onComplete(successResponse);
                    }
                }
                else
                {
                    // FALLBACK: Create new proposal if pool is empty
                    _logger.LogInformation($"[TIMING] INSTANT POOL: Sem proposta disponível, criando nova");
                    
                    var fallbackProposal = await GetFastProposalAsync(request);
                    
                    if (fallbackProposal?.Error == null && fallbackProposal?.Proposal != null)
                    {
                        var buyRequest = new
                        {
                            buy = fallbackProposal.Proposal.Id,
                            price = fallbackProposal.Proposal.AskPrice,
                            subscribe = 1
                        };
                        
                        var jsonRequest = JsonSerializer.Serialize(buyRequest);
                        _ws.Send(jsonRequest);
                        
                        var fallbackTime = DateTimeOffset.Now;
                        var fallbackDelay = (fallbackTime - instantStartTime).TotalMilliseconds;
                        _logger.LogInformation($"[TIMING] INSTANT POOL FALLBACK: Compra executada em {fallbackDelay}ms");
                        
                        if (onComplete != null)
                        {
                            var fallbackResponse = new BuyResponse
                            {
                                Buy = new BuyContract
                                {
                                    ContractId = 0,
                                    TransactionId = 0
                                }
                            };
                            onComplete(fallbackResponse);
                        }
                    }
                    else
                    {
                        _logger.LogError($"[TIMING] INSTANT POOL: Erro na proposta fallback - {fallbackProposal?.Error?.Message}");
                        
                        if (onComplete != null)
                        {
                            var errorResponse = new BuyResponse { Error = new ApiError { Message = "No proposals available" } };
                            onComplete(errorResponse);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var errorTime = DateTimeOffset.Now;
                var errorDelay = (errorTime - instantStartTime).TotalMilliseconds;
                _logger.LogError(ex, $"[TIMING] INSTANT POOL BUY: Erro após {errorDelay}ms");
                
                if (onComplete != null)
                {
                    var errorResponse = new BuyResponse { Error = new ApiError { Message = ex.Message } };
                    onComplete(errorResponse);
                }
            }
        });
    }
    
    // Ultra-fast proposal method optimized for instant reuse
    private async Task<ProposalResponse> GetHotProposalAsync(ProposalRequest request)
    {
        try
        {
            // Create proposal request with minimal overhead
            var proposalRequest = new Dictionary<string, object>
            {
                ["proposal"] = 1,
                ["contract_type"] = request.ContractType,
                ["symbol"] = request.Symbol,
                ["duration"] = request.Duration,
                ["duration_unit"] = request.DurationUnit,
                ["currency"] = request.Currency,
                ["basis"] = request.Basis,
                ["amount"] = request.Stake
            };

            // Add optional parameters
            if (!string.IsNullOrEmpty(request.Barrier))
                proposalRequest["barrier"] = request.Barrier;
                
            if (!string.IsNullOrEmpty(request.Barrier2))
                proposalRequest["barrier2"] = request.Barrier2;
                
            if (request.LastDigitPrediction.HasValue)
                proposalRequest["last_digit_prediction"] = request.LastDigitPrediction.Value;

            // Use ultra-fast request with reduced timeout for hot proposals
            var response = await SendFastRequestAsync(proposalRequest);
            var proposalResponse = response.Deserialize<ProposalResponse>();
            return proposalResponse ?? new ProposalResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter hot proposal");
            return new ProposalResponse { Error = new ProposalError { Message = ex.Message } };
        }
    }
    
    // ULTRA-FAST DIRECT SEND: Bypasses all intermediate processing for true sub-100ms execution
    public void SendDirectBuyCommand(string buyJson)
    {
        try
        {
            if (_ws?.IsRunning == true)
            {
                var sendTime = DateTimeOffset.Now;
                _ws.Send(buyJson);
                var afterSend = DateTimeOffset.Now;
                var sendDelay = (afterSend - sendTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] DIRECT SEND: WebSocket message sent in {sendDelay}ms at {afterSend:HH:mm:ss.fff}");
            }
            else
            {
                _logger.LogError("[CRITICAL] DIRECT SEND: WebSocket not running - cannot send direct command");
                throw new InvalidOperationException("WebSocket connection not available");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[CRITICAL] DIRECT SEND: Failed to send direct buy command");
            throw;
        }
    }
    
    // TICKS SUBSCRIPTION: Subscribe to real-time price ticks for selected symbol
    public async Task SubscribeToTicksAsync(string symbol)
    {
        try
        {
            // Unsubscribe from current ticks if any
            UnsubscribeFromTicks();
            
            var request = new { ticks = symbol, subscribe = 1 };
            var jsonRequest = JsonSerializer.Serialize(request);
            _ws.Send(jsonRequest);
            
            _currentTicksSubscription = symbol;
            _logger.LogInformation($"[TICKS] Subscribed to ticks for symbol: {symbol}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[TICKS] Failed to subscribe to ticks for symbol: {symbol}");
        }
    }
    
    // TICKS UNSUBSCRIPTION: Stop receiving ticks for current symbol
    public void UnsubscribeFromTicks()
    {
        try
        {
            if (!string.IsNullOrEmpty(_currentTicksSubscription))
            {
                var request = new { forget_all = "ticks" };
                var jsonRequest = JsonSerializer.Serialize(request);
                _ws.Send(jsonRequest);
                
                _logger.LogInformation($"[TICKS] Unsubscribed from ticks for symbol: {_currentTicksSubscription}");
                _currentTicksSubscription = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[TICKS] Failed to unsubscribe from ticks");
        }
    }
    
    // TICKS PROCESSING: Handle incoming tick data
    private void ProcessTickUpdate(JsonElement root)
    {
        try
        {
            if (root.TryGetProperty("tick", out var tick))
            {
                if (tick.TryGetProperty("quote", out var quoteElement) && 
                    tick.TryGetProperty("epoch", out var epochElement))
                {
                    var price = quoteElement.GetDecimal();
                    var epoch = epochElement.GetInt64();
                    var timestamp = DateTimeOffset.FromUnixTimeSeconds(epoch).DateTime;
                    
                    // Trigger tick received event
                    TickReceived?.Invoke(price, timestamp);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[TICKS] Error processing tick update");
        }
    }

    private void ProcessBuyResponse(JsonElement root)
    {
        try
        {
            if (root.TryGetProperty("buy", out var buyElement))
            {
                var contractId = buyElement.GetProperty("contract_id").GetInt64().ToString();
                var longcode = buyElement.GetProperty("longcode").GetString();
                var buyPrice = buyElement.GetProperty("buy_price").GetDecimal();
                var payout = buyElement.GetProperty("payout").GetDecimal();
                var purchaseTime = buyElement.GetProperty("purchase_time").GetInt64();

                // Extract contract type and duration from longcode
                var contractType = ExtractContractTypeFromLongcode(longcode);
                var duration = ExtractDurationFromLongcode(longcode);

                var purchaseDateTime = DateTimeOffset.FromUnixTimeSeconds(purchaseTime).DateTime;

                _logger.LogInformation($"Buy response processed: Contract {contractId}, Type: {contractType}, Stake: {buyPrice}");

                // Trigger contract purchased event
                ContractPurchased?.Invoke(contractId, contractType, duration, buyPrice, payout, purchaseDateTime);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar resposta de compra.");
        }
    }

    private string ExtractContractTypeFromLongcode(string longcode)
    {
        if (string.IsNullOrEmpty(longcode)) return "Unknown";

        // Extract contract type from longcode (simplified extraction)
        if (longcode.Contains("Rise")) return "Rise";
        if (longcode.Contains("Fall")) return "Fall";
        if (longcode.Contains("Call")) return "Call";
        if (longcode.Contains("Put")) return "Put";
        if (longcode.Contains("Higher")) return "Higher";
        if (longcode.Contains("Lower")) return "Lower";
        if (longcode.Contains("Touch")) return "Touch";
        if (longcode.Contains("No Touch")) return "No Touch";
        if (longcode.Contains("Ends Between")) return "Ends Between";
        if (longcode.Contains("Ends Outside")) return "Ends Outside";
        if (longcode.Contains("Stays Between")) return "Stays Between";
        if (longcode.Contains("Goes Outside")) return "Goes Outside";
        if (longcode.Contains("Matches")) return "Matches";
        if (longcode.Contains("Differs")) return "Differs";

        return "Unknown";
    }

    private string ExtractDurationFromLongcode(string longcode)
    {
        if (string.IsNullOrEmpty(longcode)) return "---";

        // Extract duration from longcode using regex or string parsing
        // This is a simplified extraction - you may need to improve this based on actual longcode format
        var parts = longcode.Split(' ');
        foreach (var part in parts)
        {
            if (part.Contains("tick") || part.Contains("second") || part.Contains("minute") || part.Contains("hour"))
            {
                return part;
            }
        }

        return "---";
    }
}