using Excalibur.Services;
using Excalibur.Models;
using Excalibur.Infrastructure;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.Extensions.Logging;

namespace Excalibur.ViewModels;

public class MainViewModel : ObservableObject, IDisposable
{
    private readonly IDerivApiService _derivApiService;
    private readonly ILogger<MainViewModel> _logger;

    private bool _isConnected;
    public bool IsConnected
    {
        get => _isConnected;
        set { _isConnected = value; OnPropertyChanged(); }
    }

    private string _accountCode = "-----------";
    public string AccountCode
    {
        get => _accountCode;
        set { _accountCode = value; OnPropertyChanged(); }
    }
    
    private string _accountType = "---";
    public string AccountType
    {
        get => _accountType;
        set { _accountType = value; OnPropertyChanged(); }
    }

    private double _balance;
    public double Balance
    {
        get => _balance;
        set { _balance = value; OnPropertyChanged(); }
    }

    private long _ping;
    public long Ping
    {
        get => _ping;
        set { _ping = value; OnPropertyChanged(); }
    }
    
    // Properties for Contract Info Card
    private string _selectedContractDisplayName = "Nenhum contrato selecionado";
    public string SelectedContractDisplayName
    {
        get => _selectedContractDisplayName;
        set { _selectedContractDisplayName = value; OnPropertyChanged(); }
    }
    
    // Simplified properties for compact display
    public string ContractSymbol => SelectedActiveSymbol?.Symbol ?? "---";
    public string ContractTypeDisplay => SelectedContractType != null ? $"{SelectedContractType.CategoryDisplay} - {SelectedContractType.ContractDisplay}" : "---";
    
    private decimal _currentTickPrice;
    public decimal CurrentTickPrice
    {
        get => _currentTickPrice;
        set 
        { 
            var previousPrice = _currentTickPrice;
            _currentTickPrice = value; 
            OnPropertyChanged(); 
            OnPropertyChanged(nameof(FormattedTickPrice));
            OnPropertyChanged(nameof(SpotPriceDisplay));
            
            // Update price direction
            if (previousPrice != 0 && value != previousPrice)
            {
                IsPriceUp = value > previousPrice;
                OnPropertyChanged(nameof(IsPriceUp));
                OnPropertyChanged(nameof(SpotColor));
                OnPropertyChanged(nameof(PriceArrow));
            }
        }
    }
    
    public string FormattedTickPrice
    {
        get => _currentTickPrice == 0 ? "---" : _currentTickPrice.ToString("F5");
    }
    
    // Price direction and formatting properties
    public bool IsPriceUp { get; private set; }
    
    public string SpotPriceDisplay
    {
        get
        {
            if (_currentTickPrice == 0) return "---";
            // Always show exactly 3 decimal places
            return _currentTickPrice.ToString("F3");
        }
    }
    
    public string SpotColor
    {
        get => _currentTickPrice == 0 ? "White" : (IsPriceUp ? "#FF2ECC71" : "#FFFF4444");
    }
    
    public string PriceArrow
    {
        get => _currentTickPrice == 0 ? "" : (IsPriceUp ? "▲" : "▼");
    }
    
    private DateTime _lastTickTime;
    public DateTime LastTickTime
    {
        get => _lastTickTime;
        set { _lastTickTime = value; OnPropertyChanged(); }
    }

    // Coleções para seleção de contratos
    private ObservableCollection<string> _markets = new();
    public ObservableCollection<string> Markets
    {
        get => _markets;
        set { _markets = value; OnPropertyChanged(); }
    }

    private ObservableCollection<string> _subMarkets = new();
    public ObservableCollection<string> SubMarkets
    {
        get => _subMarkets;
        set { _subMarkets = value; OnPropertyChanged(); }
    }

    private ObservableCollection<ActiveSymbol> _activeSymbols = new();
    public ObservableCollection<ActiveSymbol> ActiveSymbols
    {
        get => _activeSymbols;
        set { _activeSymbols = value; OnPropertyChanged(); }
    }

    private ObservableCollection<ContractDetails> _contractTypes = new();
    public ObservableCollection<ContractDetails> ContractTypes
    {
        get => _contractTypes;
        set { _contractTypes = value; OnPropertyChanged(); }
    }

    private ObservableCollection<ProfitTableEntry> _profitTableEntries = new();
    public ObservableCollection<ProfitTableEntry> ProfitTableEntries
    {
        get => _profitTableEntries;
        set { _profitTableEntries = value; OnPropertyChanged(); }
    }

    // Propriedade para o Profit total
    private decimal _totalProfit;
    public decimal TotalProfit
    {
        get => _totalProfit;
        set
        {
            _totalProfit = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(TotalProfitDisplay));
            OnPropertyChanged(nameof(TotalProfitColor));
        }
    }

    public string TotalProfitDisplay => TotalProfit.ToString("F2");
    public string TotalProfitColor => TotalProfit >= 0 ? "#FF2ECC71" : "#FFFF4444";

    private void UpdateTotalProfit()
    {
        // SessionProfit é atualizado em tempo real com os entries da sessão atual
        var currentSessionProfit = ProfitTableEntries.Sum(entry => entry.TotalProfitLoss);
        SessionProfit = currentSessionProfit;

        // Atualizar as propriedades de exibição
        OnPropertyChanged(nameof(SessionProfitDisplay));
        OnPropertyChanged(nameof(SessionProfitColor));

        _logger.LogInformation($"SessionProfit atualizado: {SessionProfit:F2}");

        // TotalProfit NÃO é atualizado aqui - apenas quando sessão termina

        // Verificar se Take Profit foi atingido (apenas para Martingale e Dualgale)
        // Para Dualgale, só verificar se não há contratos ativos E não há processamento em andamento
        bool canCheckTakeProfit = true;
        if (IsDualgaleEnabled)
        {
            var activeContracts = ProfitTableEntries.Count(entry => entry.IsActive);
            canCheckTakeProfit = activeContracts == 0 && !IsDualgaleProcessing; // Só verificar quando não há contratos ativos nem processamento
            _logger.LogInformation($"Dualgale - Contratos ativos: {activeContracts}, Processing: {IsDualgaleProcessing}, Pode verificar Take Profit: {canCheckTakeProfit}");
        }

        if ((IsMartingaleEnabled || IsDualgaleEnabled) && TakeProfit > 0 && SessionProfit >= TakeProfit && canCheckTakeProfit)
        {
            _logger.LogInformation($"Take Profit atingido! Session Profit: {SessionProfit:F2}, Take Profit: {TakeProfit:F2}");
            Console.WriteLine($"[SESSÃO DEBUG] Take Profit atingido! Session Profit: {SessionProfit:F2}, Take Profit: {TakeProfit:F2}");

            // Atualizar TotalProfit com o profit da sessão que terminou
            TotalProfit += SessionProfit;

            // Incrementar número da sessão atual
            CurrentSessionNumber++;
            Console.WriteLine($"[SESSÃO DEBUG] Sessão incrementada para: {CurrentSessionNumber}/{MaxSessions}");

            // Verificar se atingiu o número máximo de sessões
            Console.WriteLine($"[SESSÃO DEBUG] Verificando limite de sessões: {CurrentSessionNumber} >= {MaxSessions}?");
            if (CurrentSessionNumber >= MaxSessions)
            {
                _logger.LogInformation($"Número máximo de sessões atingido ({CurrentSessionNumber}/{MaxSessions}). Encerrando operações.");
                Console.WriteLine($"[SESSÃO DEBUG] LIMITE ATINGIDO! Encerrando operações. Sessões: {CurrentSessionNumber}/{MaxSessions}");

                // Encerrar operações - desativar todos os modos
                IsMartingaleEnabled = false;
                IsDualgaleEnabled = false;
                IsNoneSelected = true;

                // Limpar registros da tabela mas manter estatísticas
                ClearProfitTableKeepingStats();

                return;
            }

            Console.WriteLine($"[SESSÃO DEBUG] Limite não atingido. Continuando operações. Sessões: {CurrentSessionNumber}/{MaxSessions}");

            _logger.LogInformation($"Iniciando nova sessão {CurrentSessionNumber}/{MaxSessions}");

            // Limpar registros da tabela mas manter estatísticas
            ClearProfitTableKeepingStats();

            // Resetar para o valor original do campo Stake no início de nova sessão
            Console.WriteLine($"[NOVA SESSÃO DEBUG] Resetando para valor original do campo Stake");
            Console.WriteLine($"[NOVA SESSÃO DEBUG] _originalStakeValue atual: '{_originalStakeValue}'");
            Console.WriteLine($"[NOVA SESSÃO DEBUG] InitialStakeAmount atual: {InitialStakeAmount:F2}");

            // Garantir que temos um valor válido para resetar
            decimal stakeToRestore = 0;
            string stakeStringToRestore = "";

            if (!string.IsNullOrEmpty(_originalStakeValue))
            {
                var normalizedStake = _originalStakeValue.Replace(',', '.');
                if (decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal originalStake) && originalStake > 0)
                {
                    stakeToRestore = originalStake;
                    stakeStringToRestore = _originalStakeValue;
                    Console.WriteLine($"[NOVA SESSÃO DEBUG] Usando _originalStakeValue: {_originalStakeValue} ({originalStake:F2})");
                }
            }
            
            // Fallback 1: usar InitialStakeAmount se _originalStakeValue não estiver disponível
            if (stakeToRestore == 0 && InitialStakeAmount > 0)
            {
                stakeToRestore = InitialStakeAmount;
                stakeStringToRestore = InitialStakeAmount.ToString("F2");
                Console.WriteLine($"[NOVA SESSÃO DEBUG] Usando InitialStakeAmount como fallback: {InitialStakeAmount:F2}");
            }
            
            // Fallback 2: usar valor padrão se nenhum dos anteriores estiver disponível
            if (stakeToRestore == 0)
            {
                stakeToRestore = 0.40m;
                stakeStringToRestore = "0.40";
                Console.WriteLine($"[NOVA SESSÃO DEBUG] AVISO: Usando valor padrão 0.40 - nenhum valor original encontrado!");
                
                // Capturar este valor como original para futuras sessões
                if (string.IsNullOrEmpty(_originalStakeValue))
                {
                    _originalStakeValue = stakeStringToRestore;
                    Console.WriteLine($"[NOVA SESSÃO DEBUG] Definindo _originalStakeValue para valor padrão: {_originalStakeValue}");
                }
            }

            // Aplicar os valores restaurados
            StakeAmount = stakeStringToRestore;
            InitialStakeAmount = stakeToRestore;
            NextStakeAmount = stakeToRestore;
            
            Console.WriteLine($"[NOVA SESSÃO DEBUG] Valores finais - StakeAmount: '{StakeAmount}', InitialStakeAmount: {InitialStakeAmount:F2}, NextStakeAmount: {NextStakeAmount:F2}");

            // Reset dos sistemas de trading para nova sessão
            if (IsMartingaleEnabled)
            {
                ResetMartingale();
            }
            else if (IsDualgaleEnabled)
            {
                ResetDualgale();
            }

            // Iniciar entradas automáticas na nova sessão
            Console.WriteLine($"[NOVA SESSÃO DEBUG] Chamando StartNewSession...");
            StartNewSession();
        }
    }

    // Propriedades para seleções atuais
    private string _selectedMarket;
    public string SelectedMarket
    {
        get => _selectedMarket;
        set
        {
            _selectedMarket = value;
            OnPropertyChanged();
            OnMarketSelectionChanged();
        }
    }

    private decimal CalculateNextMartingaleStake()
    {
        if (InitialStakeAmount == 0)
        {
            var normalizedStake = StakeAmount?.Replace(',', '.');
            if (decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal initialStake))
            {
                InitialStakeAmount = initialStake;
            }
        }

        var nextLevel = CurrentMartingaleLevel + 1;
        if (nextLevel == 1)
        {
            return InitialStakeAmount;
        }
        else
        {
            return Math.Round(InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, nextLevel - 1), 2);
        }
    }

    private string _selectedSubMarket;
    public string SelectedSubMarket
    {
        get => _selectedSubMarket;
        set
        {
            _selectedSubMarket = value;
            OnPropertyChanged();
            OnSubMarketSelectionChanged();
        }
    }

    private ActiveSymbol _selectedActiveSymbol;
    public ActiveSymbol SelectedActiveSymbol
    {
        get => _selectedActiveSymbol;
        set
        {
            _selectedActiveSymbol = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(ContractSymbol));
            
            // Update chart title and clear previous data only if NOT restoring after reconnection
            if (ChartViewModel != null)
            {
                if (value != null)
                {
                    ChartViewModel.UpdateChartTitle(value.Symbol);
                    // Only clear data if this is a user-initiated change, not a restoration
                    if (!_isRestoringSelections)
                    {
                        ChartViewModel.ClearData(); // Clear previous symbol's data
                    }
                }
                else
                {
                    ChartViewModel.UpdateChartTitle(null);
                    if (!_isRestoringSelections)
                    {
                        ChartViewModel.ClearData();
                    }
                }
            }
            
            OnActiveSymbolSelectionChanged();
        }
    }

    private ContractDetails _selectedContractType;
    public ContractDetails SelectedContractType
    {
        get => _selectedContractType;
        set
        {
            _selectedContractType = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(ContractTypeDisplay));
            UpdateContractParameters(); // Método chave para atualizar a UI
            
            // Update contract display name and subscribe to ticks
            if (value != null && SelectedActiveSymbol != null)
            {
                // Subscribe to ticks for the selected symbol
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _derivApiService.SubscribeToTicksAsync(SelectedActiveSymbol.Symbol);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to subscribe to ticks for selected symbol");
                    }
                });
            }
            else
            {
                _derivApiService.UnsubscribeFromTicks();
            }
        }
    }

    // Novas propriedades para controlar a visibilidade da UI
    private bool _isDurationVisible;
    public bool IsDurationVisible { get => _isDurationVisible; set { _isDurationVisible = value; OnPropertyChanged(); } }

    private bool _isBarrier1Visible;
    public bool IsBarrier1Visible { get => _isBarrier1Visible; set { _isBarrier1Visible = value; OnPropertyChanged(); } }

    private bool _isBarrier2Visible;
    public bool IsBarrier2Visible { get => _isBarrier2Visible; set { _isBarrier2Visible = value; OnPropertyChanged(); } }

    private bool _isDigitSelectionVisible;
    public bool IsDigitSelectionVisible { get => _isDigitSelectionVisible; set { _isDigitSelectionVisible = value; OnPropertyChanged(); } }

    private string _durationInfo = string.Empty;
    public string DurationInfo { get => _durationInfo; set { _durationInfo = value; OnPropertyChanged(); } }

    // Propriedades para Stake e cálculo de payout
    private decimal _stake = 10.0m;
    public decimal Stake 
    { 
        get => _stake; 
        set 
        { 
            _stake = Math.Round(value, 2); 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    // StakeAmount property moved to end of class to integrate with Martingale

    private string _barrier1Value = string.Empty;
    public string Barrier1Value 
    { 
        get => _barrier1Value; 
        set 
        { 
            _barrier1Value = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private string _barrier2Value = string.Empty;
    public string Barrier2Value 
    { 
        get => _barrier2Value; 
        set 
        { 
            _barrier2Value = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private int _durationValue = 5;
    public int DurationValue 
    { 
        get => _durationValue; 
        set 
        { 
            _durationValue = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private string _durationUnit = "t";
    public string DurationUnit 
    { 
        get => _durationUnit; 
        set 
        { 
            _durationUnit = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    private int _selectedDigit = 0;
    public int SelectedDigit 
    { 
        get => _selectedDigit; 
        set 
        { 
            _selectedDigit = value; 
            OnPropertyChanged(); 
            CalculateProposalAsync(); 
        } 
    }

    // Propriedades para exibir resultados do cálculo
    private decimal _calculatedPayout;
    public decimal CalculatedPayout { get => _calculatedPayout; set { _calculatedPayout = value; OnPropertyChanged(); } }

    private decimal _askPrice;
    public decimal AskPrice
    {
        get => _askPrice;
        set { _askPrice = value; OnPropertyChanged(); }
    }

    private string _currentProposalId;
    public string CurrentProposalId
    {
        get => _currentProposalId;
        set { _currentProposalId = value; OnPropertyChanged(); }
    }

    private string _calculatedBarrier1 = string.Empty;
    public string CalculatedBarrier1 { get => _calculatedBarrier1; set { _calculatedBarrier1 = value; OnPropertyChanged(); } }

    private string _calculatedBarrier2 = string.Empty;
    public string CalculatedBarrier2 { get => _calculatedBarrier2; set { _calculatedBarrier2 = value; OnPropertyChanged(); } }

    private string _barrier1Suggestion = string.Empty;
    public string Barrier1Suggestion { get => _barrier1Suggestion; set { _barrier1Suggestion = value; OnPropertyChanged(); } }

    private string _barrier2Suggestion = string.Empty;
    public string Barrier2Suggestion { get => _barrier2Suggestion; set { _barrier2Suggestion = value; OnPropertyChanged(); } }

    private bool _isCalculating;
    public bool IsCalculating { get => _isCalculating; set { _isCalculating = value; OnPropertyChanged(); } }

    // Propriedades do Martingale
    private bool _isMartingaleEnabled;
    public bool IsMartingaleEnabled
    {
        get => _isMartingaleEnabled;
        set
        {
            _isMartingaleEnabled = value;
            OnPropertyChanged();
            if (value)
            {
                IsNoneSelected = false;
                IsDualgaleEnabled = false;
                // Reset martingale state when enabled
                CurrentMartingaleLevel = 0;
                var normalizedStake = StakeAmount?.Replace(',', '.');
                NextStakeAmount = decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal currentStake) ? currentStake : 0;
                // CORREÇÃO: NÃO resetar CurrentSessionNumber - deve manter continuidade
                Console.WriteLine($"[MARTINGALE] Modo ativado - mantendo sessão atual: {CurrentSessionNumber}");
            }
            else
            {
                // Reset martingale state when disabled
                CurrentMartingaleLevel = 0;
                var normalizedStake = StakeAmount?.Replace(',', '.');
                NextStakeAmount = decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal currentStake) ? currentStake : 0;
            }
        }
    }

    private decimal _martingaleFactor = 2.0m;
    public decimal MartingaleFactor 
    { 
        get => _martingaleFactor; 
        set 
        { 
            // Arredonda para duas casas decimais
            _martingaleFactor = Math.Round(value, 2); 
            OnPropertyChanged();
            CalculateNextStake();
        } 
    }

    private int _martingaleLevel = 3;
    public int MartingaleLevel 
    { 
        get => _martingaleLevel; 
        set 
        { 
            _martingaleLevel = value; 
            OnPropertyChanged();
        } 
    }

    private bool _isFastMartingale;
    public bool IsFastMartingale 
    { 
        get => _isFastMartingale; 
        set 
        { 
            _isFastMartingale = value; 
            OnPropertyChanged();
            
            if (value && IsMartingaleEnabled)
            {
                _logger.LogInformation("[DEBUG] Fast Martingale ENABLED - triggering IMMEDIATE aggressive pool population");
                
                // IMMEDIATE AGGRESSIVE POPULATION: Ensure pool is ready instantly
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await PopulateHotProposalPoolImmediate();
                        
                        lock (_poolLock)
                        {
                            var readyLevels = _hotProposalPool.Count;
                            _logger.LogInformation($"[DEBUG] Fast Martingale READY: {readyLevels} proposals pre-calculated and ready for instant execution");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[CRITICAL] Fast Martingale enable: Failed to populate hot pool");
                    }
                });
            }
            else if (!value)
            {
                _logger.LogInformation("[DEBUG] Fast Martingale DISABLED - clearing hot pool");
                
                // Clear pool when disabled to save memory
                lock (_poolLock)
                {
                    _hotProposalPool.Clear();
                }
            }
        } 
    }

    // HOT PROPOSAL POOL - Pre-calculated proposals ready for instant execution
    private readonly Dictionary<int, ProposalResponse> _hotProposalPool = new();
    private readonly object _poolLock = new object();
    private bool _isPoolPopulating = false;


    private decimal _nextStakeAmount;
    public decimal NextStakeAmount 
    { 
        get => _nextStakeAmount; 
        set 
        { 
            _nextStakeAmount = value; 
            OnPropertyChanged();
        } 
    }

    private decimal _initialStakeAmount;
    private string _originalStakeValue; // Valor original do campo Stake antes do programa começar

    // Propriedade para o radiobutton "Nenhum"
    private bool _isNoneSelected = true;
    public bool IsNoneSelected
    {
        get => _isNoneSelected;
        set
        {
            _isNoneSelected = value;
            OnPropertyChanged();
            if (value)
            {
                IsMartingaleEnabled = false;
                IsDualgaleEnabled = false;
            }
        }
    }

    // Propriedades do Dualgale
    private bool _isDualgaleEnabled;
    public bool IsDualgaleEnabled
    {
        get => _isDualgaleEnabled;
        set
        {
            Console.WriteLine($"[DUALGALE DEBUG] IsDualgaleEnabled sendo definido para: {value}");
            _logger.LogInformation($"[DEBUG] IsDualgaleEnabled sendo definido para: {value}");
            _isDualgaleEnabled = value;
            OnPropertyChanged();
            if (value)
            {
                Console.WriteLine("[DUALGALE DEBUG] Dualgale ATIVADO - resetando outros modos");
                _logger.LogInformation("[DEBUG] Dualgale ATIVADO - resetando outros modos");
                IsNoneSelected = false;
                IsMartingaleEnabled = false;
                // Reset dualgale state when enabled
                CurrentDualgaleLevel = 0;
                DualgaleLostStake = 0;
                var normalizedStake = StakeAmount?.Replace(',', '.');
                NextStakeAmount = decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal currentStake) ? currentStake : 0;
                // CORREÇÃO: NÃO resetar CurrentSessionNumber - deve manter continuidade
                Console.WriteLine($"[DUALGALE] Modo ativado - mantendo sessão atual: {CurrentSessionNumber}");

                // Iniciar watchdog para monitorar atividade do Dualgale
                StartDualgaleWatchdog();
            }
            else
            {
                Console.WriteLine("[DUALGALE DEBUG] Dualgale DESATIVADO - resetando estado");
                _logger.LogInformation("[DEBUG] Dualgale DESATIVADO - resetando estado");
                // Reset dualgale state when disabled
                CurrentDualgaleLevel = 0;
                DualgaleLostStake = 0;
                var normalizedStake = StakeAmount?.Replace(',', '.');
                NextStakeAmount = decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal currentStake) ? currentStake : 0;

                // Parar watchdog
                StopDualgaleWatchdog();
            }
        }
    }

    private decimal _dualgaleFactor = 2.0m;
    public decimal DualgaleFactor
    {
        get => _dualgaleFactor;
        set
        {
            _dualgaleFactor = Math.Round(value, 2);
            OnPropertyChanged();
            CalculateNextStake();
        }
    }

    private int _dualgaleLevel = 3;
    public int DualgaleLevel
    {
        get => _dualgaleLevel;
        set
        {
            _dualgaleLevel = value;
            OnPropertyChanged();
        }
    }

    private decimal _dualgaleRecoverPercent = 100.0m;
    public decimal DualgaleRecoverPercent
    {
        get => _dualgaleRecoverPercent;
        set
        {
            _dualgaleRecoverPercent = Math.Round(value, 2);
            OnPropertyChanged();
            CalculateNextStake();
        }
    }

    private int _currentDualgaleLevel;
    public int CurrentDualgaleLevel
    {
        get => _currentDualgaleLevel;
        set
        {
            _currentDualgaleLevel = value;
            OnPropertyChanged();
            CalculateNextStake();
        }
    }

    private decimal _dualgaleLostStake;
    public decimal DualgaleLostStake
    {
        get => _dualgaleLostStake;
        set
        {
            _dualgaleLostStake = value;
            OnPropertyChanged();
        }
    }

    // Propriedades para rastreamento de contratos Dualgale
    private readonly Dictionary<string, DualgaleContractPair> _activeDualgalePairs = new();
    private readonly object _dualgalePairsLock = new object();

    // Propriedades do Take Profit
    private decimal _takeProfit = 0;
    public decimal TakeProfit
    {
        get => _takeProfit;
        set
        {
            _takeProfit = Math.Round(value, 2);
            OnPropertyChanged();
        }
    }

    private int _maxSessions = 1000; // Valor padrão alto para permitir operação contínua
    public int MaxSessions
    {
        get => _maxSessions;
        set
        {
            _maxSessions = value;
            OnPropertyChanged();
        }
    }

    private int _currentSessionNumber = 0;
    public int CurrentSessionNumber
    {
        get => _currentSessionNumber;
        set
        {
            _currentSessionNumber = value;
            OnPropertyChanged();
        }
    }

    // Controle para evitar entradas duplas simultâneas
    private bool _isDualgaleProcessing = false;
    public bool IsDualgaleProcessing
    {
        get => _isDualgaleProcessing;
        set
        {
            _isDualgaleProcessing = value;
            OnPropertyChanged();

            // Atualizar timestamp da última atividade quando há mudança no processamento
            if (value)
            {
                _lastDualgaleActivity = DateTime.UtcNow;
            }
        }
    }

    // Watchdog para monitorar atividade do Dualgale
    private Timer _dualgaleWatchdog;
    private DateTime _lastDualgaleActivity = DateTime.UtcNow;
    private readonly object _watchdogLock = new object();

    // Propriedades para estatísticas da sessão
    private int _sessionCount = 0;
    public int SessionCount
    {
        get => _sessionCount;
        set
        {
            _sessionCount = value;
            OnPropertyChanged();
        }
    }

    private decimal _maxStakeUsed = 0;
    public decimal MaxStakeUsed
    {
        get => _maxStakeUsed;
        set
        {
            _maxStakeUsed = value;
            OnPropertyChanged();
        }
    }

    private int _maxStakeSession = 0;
    public int MaxStakeSession
    {
        get => _maxStakeSession;
        set
        {
            _maxStakeSession = value;
            OnPropertyChanged();
        }
    }

    private int _maxLevel = 0;
    public int MaxLevel
    {
        get => _maxLevel;
        set
        {
            _maxLevel = value;
            OnPropertyChanged();
        }
    }

    // Propriedades para exibir as estatísticas globais na interface
    public int GlobalSessionCount => ProfitTableEntry.GlobalSessionCount;
    public decimal GlobalMaxStakeUsed => ProfitTableEntry.GlobalMaxStakeUsed;
    public int GlobalMaxStakeSession => ProfitTableEntry.GlobalMaxStakeSession;
    public int GlobalMaxLevel => ProfitTableEntry.GlobalMaxLevel;

    // Profit da sessão atual (resetado a cada nova sessão)
    private decimal _sessionProfit = 0;
    public decimal SessionProfit
    {
        get => _sessionProfit;
        set
        {
            _sessionProfit = value;
            OnPropertyChanged();
        }
    }

    public string SessionProfitDisplay => SessionProfit.ToString("F2");
    public string SessionProfitColor => SessionProfit >= 0 ? "#FF2ECC71" : "#FFFF4444";

    public class DualgaleContractPair
    {
        public string? HigherContractId { get; set; }
        public string? LowerContractId { get; set; }
        public decimal HigherStake { get; set; }
        public decimal LowerStake { get; set; }
        public decimal HigherPayout { get; set; }
        public decimal LowerPayout { get; set; }
        public bool HigherFinished { get; set; }
        public bool LowerFinished { get; set; }
        public decimal? HigherProfit { get; set; }
        public decimal? LowerProfit { get; set; }
        public DateTime CreatedAt { get; set; }

        public bool BothFinished => HigherFinished && LowerFinished;
        public decimal TotalLoss => (HigherProfit ?? 0) + (LowerProfit ?? 0);
        public decimal LostStake => HigherProfit < 0 ? HigherStake : LowerStake;
        public decimal WinningPayout => HigherProfit > 0 ? HigherPayout : LowerPayout;
        public bool HigherLost => HigherProfit < 0;
        public bool LowerLost => LowerProfit < 0;
        public string LosingContractType => HigherLost ? "CALL" : "PUT";
    }
    public decimal InitialStakeAmount 
    { 
        get => _initialStakeAmount; 
        set 
        { 
            _initialStakeAmount = value; 
            OnPropertyChanged();
        } 
    }

    private int _currentMartingaleLevel;
    public int CurrentMartingaleLevel 
    { 
        get => _currentMartingaleLevel; 
        set 
        { 
            _currentMartingaleLevel = value; 
            OnPropertyChanged();
            CalculateNextStake();
        } 
    }

    // Lista completa de símbolos ativos para filtragem
    private List<ActiveSymbol> _allActiveSymbols = new();
    
    // Flag to prevent clearing during restoration
    private bool _isRestoringSelections = false;
    
    // Chart functionality
    private ChartViewModel _chartViewModel;
    public ChartViewModel ChartViewModel
    {
        get => _chartViewModel;
        set { _chartViewModel = value; OnPropertyChanged(); }
    }
    
    // Chart zoom commands
    public ICommand ZoomToFiveMinutesCommand { get; private set; }
    public ICommand ZoomToFifteenMinutesCommand { get; private set; }
    public ICommand ZoomToOneHourCommand { get; private set; }
    public ICommand ZoomToAllCommand { get; private set; }
    
    // Chart time unit commands
    public ICommand ChartTimeUnitTicksCommand { get; private set; }
    public ICommand ChartTimeUnitSecondsCommand { get; private set; }
    public ICommand ChartTimeUnitMinutesCommand { get; private set; }
    
    public MainViewModel(IDerivApiService derivApiService, ILogger<MainViewModel> logger)
    {
        _derivApiService = derivApiService;
        _logger = logger;
        _chartViewModel = new ChartViewModel();

        // Initialize chart zoom commands
        ZoomToFiveMinutesCommand = new RelayCommand(() => ChartViewModel?.ZoomToLast(5));
        ZoomToFifteenMinutesCommand = new RelayCommand(() => ChartViewModel?.ZoomToLast(15));
        ZoomToOneHourCommand = new RelayCommand(() => ChartViewModel?.ZoomToLast(60));
        ZoomToAllCommand = new RelayCommand(() => ChartViewModel?.ResetZoom());

        // Initialize chart time unit commands
        ChartTimeUnitTicksCommand = new RelayCommand(() => ChartViewModel?.SetTimeUnit("ticks"));
        ChartTimeUnitSecondsCommand = new RelayCommand(() => ChartViewModel?.SetTimeUnit("seconds"));
        ChartTimeUnitMinutesCommand = new RelayCommand(() => ChartViewModel?.SetTimeUnit("minutes"));

        SubscribeToApiEvents();
        _derivApiService.ConnectAndAuthorizeAsync();
    }

    // Implementar IDisposable para limpeza adequada
    public void Dispose()
    {
        try
        {
            // Parar watchdog
            StopDualgaleWatchdog();

            // Limpar pares ativos
            lock (_dualgalePairsLock)
            {
                _activeDualgalePairs.Clear();
            }

            Console.WriteLine("[DISPOSE] MainViewModel disposed - recursos limpos");
            _logger.LogInformation("MainViewModel disposed - recursos limpos");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[DISPOSE] Erro durante dispose: {ex.Message}");
            _logger.LogError(ex, "Erro durante dispose do MainViewModel");
        }
    }

    private void SubscribeToApiEvents()
    {
        _derivApiService.ConnectionEstablished += OnConnectionEstablished;
        _derivApiService.ConnectionLost += OnConnectionLost;
        _derivApiService.AccountInfoUpdated += OnAccountInfoUpdated;
        _derivApiService.PingUpdated += OnPingUpdated;
        _derivApiService.ContractResult += OnContractResultReceived;
        _derivApiService.ContractNearExpiry += OnContractNearExpiry;
        _derivApiService.TickReceived += OnTickReceived;
        _derivApiService.ContractFinished += OnContractFinished;
        _derivApiService.ContractPurchased += OnContractPurchased;
    }

    private void OnPingUpdated(long newPing)
    {
        Application.Current.Dispatcher.Invoke(() => Ping = newPing);
    }

    private void OnContractResultReceived(bool isWin)
    {
        var receivedTime = DateTimeOffset.Now;
        
        // ULTRA-IMMEDIATE EXECUTION: Zero overhead processing
        if (IsMartingaleEnabled)
        {
            if (isWin)
            {
                _logger.LogInformation($"[TIMING] Contract WIN at {receivedTime:HH:mm:ss.fff} - calling OnContractWin");
                // Fire-and-forget for win processing
                _ = Task.Run(() => OnContractWin());
            }
            else
            {
                _logger.LogInformation($"[TIMING] Contract LOSS at {receivedTime:HH:mm:ss.fff} - ZERO-DELAY EXECUTION");
                // ZERO-DELAY EXECUTION: Inline call with no overhead
                var executionStart = DateTimeOffset.Now;
                OnContractLossUltraFast();
                var executionEnd = DateTimeOffset.Now;
                var totalExecution = (executionEnd - executionStart).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] ZERO-DELAY: Complete execution in {totalExecution}ms");
            }
        }
        else
        {
            _logger.LogInformation("[DEBUG] Martingale not enabled, ignoring contract result");
        }
    }

    private void OnContractNearExpiry(string contractId)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            _logger.LogInformation($"[DEBUG] OnContractNearExpiry chamado para contrato: {contractId}, IsMartingaleEnabled = {IsMartingaleEnabled}, IsFastMartingale = {IsFastMartingale}");
            
            // Ensure hot pool is ready for instant execution if needed
            if (IsMartingaleEnabled && IsFastMartingale)
            {
                _logger.LogInformation($"[DEBUG] Contrato próximo ao vencimento - garantindo HOT POOL pronto para execução instantânea");
                
                // Trigger immediate pool verification and replenishment if needed
                _ = Task.Run(async () =>
                {
                    lock (_poolLock)
                    {
                        var availableProposals = _hotProposalPool.Count;
                        _logger.LogInformation($"[DEBUG] HOT POOL status: {availableProposals} propostas disponíveis");
                        
                        if (availableProposals < 2)
                        {
                            _logger.LogInformation("[DEBUG] HOT POOL com poucas propostas - iniciando reabastecimento de emergencia");
                        }
                    }
                    
                    // Always ensure pool is fully populated before potential loss
                    await PopulateHotProposalPool();
                });
            }
        });
    }
    
    // Tick update event handler
    private void OnTickReceived(decimal price, DateTime timestamp)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            CurrentTickPrice = price;
            LastTickTime = timestamp;

            // Enable continuous auto-scroll to ensure latest data is always visible
            ChartViewModel?.EnableContinuousAutoScroll();

            // Update chart with new tick data
            ChartViewModel?.AddTickData(price, timestamp);

            // Update active profit table entries with current price
            UpdateActiveProfitTableEntries(price);
        });
    }


    private void OnAccountInfoUpdated(string accountCode, string accountType, double balance)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            AccountCode = accountCode;
            // O tipo de conta só vem na autorização, então não atualizamos se for nulo
            if (accountType != null) 
            {
                AccountType = accountType;
            }
            Balance = balance;
        });
    }

    private void OnConnectionLost()
    {
        Application.Current.Dispatcher.Invoke(() => IsConnected = false);
    }

    private void OnConnectionEstablished()
    {
        Application.Current.Dispatcher.Invoke(() => 
        {
            _logger.LogInformation("[DEBUG] ConnectionEstablished evento disparado");
            IsConnected = true;
            // Preserva as seleções atuais para restaurar após reconexão
            var previousMarket = SelectedMarket;
            var previousSubMarket = SelectedSubMarket;
            var previousActiveSymbol = SelectedActiveSymbol?.Symbol;
            var previousContractType = SelectedContractType?.ContractType;
            
            // Carrega os símbolos ativos quando a conexão é estabelecida
            _ = LoadActiveSymbolsAsync().ContinueWith(async _ => 
            {
                // Restaura as seleções após carregar os dados
                await RestoreSelectionsAsync(previousMarket, previousSubMarket, previousActiveSymbol, previousContractType);
                
                // Ativa subscrição para atualizações de saldo em tempo real
                await _derivApiService.SubscribeToBalanceAsync();
            });
        });
    }

    // Métodos para carregar dados da API
    private async Task LoadActiveSymbolsAsync()
    {
        try
        {
            _logger.LogInformation("[DEBUG] LoadActiveSymbolsAsync iniciado");
            var symbols = await _derivApiService.GetActiveSymbolsAsync();
            _logger.LogInformation($"[DEBUG] Recebidos {symbols.Count} símbolos ativos");
            _allActiveSymbols = symbols;
            
            Application.Current.Dispatcher.Invoke(() =>
            {
                // Extrai mercados únicos
                var markets = symbols.Select(s => s.MarketDisplayName).Distinct().OrderBy(m => m).ToList();
                _logger.LogInformation($"[DEBUG] Extraídos {markets.Count} mercados únicos");
                Markets.Clear();
                foreach (var market in markets)
                {
                    Markets.Add(market);
                    _logger.LogInformation($"[DEBUG] Adicionado mercado: {market}");
                }
                _logger.LogInformation($"[DEBUG] Markets.Count após carregamento: {Markets.Count}");
                _logger.LogInformation($"[DEBUG] Markets contém: {string.Join(", ", Markets)}");
                
                // Força notificação de mudança na propriedade Markets
                OnPropertyChanged(nameof(Markets));
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DEBUG] Erro em LoadActiveSymbolsAsync: {ex.Message}");
            // Log do erro - em uma implementação real, você usaria um logger
            System.Diagnostics.Debug.WriteLine($"Erro ao carregar símbolos ativos: {ex.Message}");
        }
    }

    private async Task RestoreSelectionsAsync(string previousMarket, string previousSubMarket, string previousActiveSymbol, string previousContractType)
    {
        try
        {
            _isRestoringSelections = true;
            await Application.Current.Dispatcher.InvokeAsync(async () =>
            {
                // Restaura o mercado se ainda existir
                if (!string.IsNullOrEmpty(previousMarket) && Markets.Contains(previousMarket))
                {
                    SelectedMarket = previousMarket;
                    
                    // Aguarda submercados carregarem
                    await Task.Delay(100); // Increased delay for better reliability
                    
                    // Restaura o submercado se ainda existir
                    if (!string.IsNullOrEmpty(previousSubMarket) && SubMarkets.Contains(previousSubMarket))
                    {
                        SelectedSubMarket = previousSubMarket;
                        
                        // Aguarda símbolos ativos carregarem
                        await Task.Delay(100);
                        
                        // Restaura o símbolo ativo se ainda existir
                        if (!string.IsNullOrEmpty(previousActiveSymbol))
                        {
                            var symbolToRestore = ActiveSymbols.FirstOrDefault(s => s.Symbol == previousActiveSymbol);
                            if (symbolToRestore != null)
                            {
                                SelectedActiveSymbol = symbolToRestore;
                                
                                // Aguarda tipos de contrato carregarem com mais tempo
                                await Task.Delay(200);
                                
                                // Restaura o tipo de contrato se ainda existir
                                if (!string.IsNullOrEmpty(previousContractType))
                                {
                                    // Tenta múltiplas vezes se a lista ainda estiver vazia
                                    for (int i = 0; i < 10; i++) // Increased attempts
                                    {
                                        var contractToRestore = ContractTypes.FirstOrDefault(c => c.ContractType == previousContractType);
                                        if (contractToRestore != null)
                                        {
                                            SelectedContractType = contractToRestore;
                                            _logger.LogInformation($"[DEBUG] Tipo de contrato restaurado: {previousContractType}");
                                            break;
                                        }
                                        else if (ContractTypes.Count == 0 && i < 9)
                                        {
                                            _logger.LogInformation($"[DEBUG] Lista de contratos ainda vazia, tentativa {i + 1}/10");
                                            await Task.Delay(100);
                                        }
                                        else
                                        {
                                            _logger.LogWarning($"[DEBUG] Tipo de contrato {previousContractType} não encontrado na lista atual após {i + 1} tentativas");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                _logger.LogInformation($"[DEBUG] Seleções restauradas: Market={SelectedMarket}, SubMarket={SelectedSubMarket}, Symbol={SelectedActiveSymbol?.Symbol}, ContractType={SelectedContractType?.ContractType}");
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DEBUG] Erro ao restaurar seleções: {ex.Message}");
        }
        finally
        {
            _isRestoringSelections = false;
        }
    }

    // Métodos para lógica de seleção em cascata
    private void OnMarketSelectionChanged()
    {
        if (string.IsNullOrEmpty(SelectedMarket))
        {
            if (!_isRestoringSelections)
            {
                SubMarkets.Clear();
                ActiveSymbols.Clear();
                ContractTypes.Clear();
            }
            return;
        }

        // Filtra submercados baseado no mercado selecionado
        var subMarkets = _allActiveSymbols
            .Where(s => s.MarketDisplayName == SelectedMarket)
            .Select(s => s.SubmarketDisplayName)
            .Distinct()
            .OrderBy(sm => sm)
            .ToList();

        SubMarkets.Clear();
        foreach (var subMarket in subMarkets)
        {
            SubMarkets.Add(subMarket);
        }

        // Limpa seleções subsequentes apenas se não estiver restaurando
        if (!_isRestoringSelections)
        {
            SelectedSubMarket = null;
            ActiveSymbols.Clear();
            ContractTypes.Clear();
        }
    }

    private void OnSubMarketSelectionChanged()
    {
        if (string.IsNullOrEmpty(SelectedSubMarket))
        {
            if (!_isRestoringSelections)
            {
                ActiveSymbols.Clear();
                ContractTypes.Clear();
            }
            return;
        }

        // Filtra símbolos ativos baseado no submercado selecionado
        var symbols = _allActiveSymbols
            .Where(s => s.MarketDisplayName == SelectedMarket && s.SubmarketDisplayName == SelectedSubMarket)
            .OrderBy(s => s.DisplayName)
            .ToList();

        ActiveSymbols.Clear();
        foreach (var symbol in symbols)
        {
            ActiveSymbols.Add(symbol);
        }

        // Limpa seleções subsequentes apenas se não estiver restaurando
        if (!_isRestoringSelections)
        {
            SelectedActiveSymbol = null;
            ContractTypes.Clear();
        }
    }

    private async void OnActiveSymbolSelectionChanged()
    {
        if (SelectedActiveSymbol == null)
        {
            if (!_isRestoringSelections)
            {
                ContractTypes.Clear();
            }
            return;
        }

        try
        {
            var contractsResponse = await _derivApiService.GetContractsForSymbolAsync(SelectedActiveSymbol.Symbol);
            
            Application.Current.Dispatcher.Invoke(() =>
            {
                ContractTypes.Clear();
                
                // Only clear SelectedContractType if not restoring
                if (!_isRestoringSelections)
                {
                    SelectedContractType = null; // Isso vai chamar o UpdateContractParameters e limpar a UI
                }
                
                foreach (var contract in contractsResponse.Available.OrderBy(c => c.CategoryDisplay).ThenBy(c => c.ContractDisplay))
                {
                    ContractTypes.Add(contract);
                }
            });
        }
        catch (Exception ex)
        {
            // Log do erro
            System.Diagnostics.Debug.WriteLine($"Erro ao carregar contratos para {SelectedActiveSymbol.Symbol}: {ex.Message}");
        }
    }

    // Lógica para atualizar a UI com base no contrato selecionado
    private void UpdateContractParameters()
    {
        if (SelectedContractType == null)
        {
            IsDurationVisible = false;
            IsBarrier1Visible = false;
            IsBarrier2Visible = false;
            IsDigitSelectionVisible = false;
            DurationInfo = string.Empty;
            Barrier1Suggestion = string.Empty;
            Barrier2Suggestion = string.Empty;
            return;
        }

        // Reset
        IsBarrier1Visible = false;
        IsBarrier2Visible = false;
        IsDigitSelectionVisible = false;
        Barrier1Suggestion = string.Empty;
        Barrier2Suggestion = string.Empty;

        // Sempre mostrar duração
        IsDurationVisible = true;
        DurationInfo = $"Duração: Min {SelectedContractType.MinContractDuration}, Max {SelectedContractType.MaxContractDuration}";

        // Verificar Barreiras e definir sugestões
        if (SelectedContractType.Barriers.HasValue)
        {
            if (SelectedContractType.Barriers >= 1)
            {
                IsBarrier1Visible = true;
                Barrier1Suggestion = GetBarrierSuggestion(SelectedContractType.ContractType, 1);
            }
            if (SelectedContractType.Barriers == 2)
            {
                IsBarrier2Visible = true;
                Barrier2Suggestion = GetBarrierSuggestion(SelectedContractType.ContractType, 2);
            }
        }

        // Verificar Dígitos
        if (SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
        {
            IsDigitSelectionVisible = true;
        }

        // Recalcular proposta quando parâmetros mudarem
        CalculateProposalAsync();
    }

    private string GetBarrierSuggestion(string contractType, int barrierNumber)
    {
        return contractType?.ToUpper() switch
        {
            "HIGHER" => "+10.5",
            "LOWER" => "-10.5",
            "TOUCH" => "+15.0",
            "NOTOUCH" => "+20.0",
            "STAYS_IN" when barrierNumber == 1 => "+25.0",
            "STAYS_IN" when barrierNumber == 2 => "-25.0",
            "GOES_OUT" when barrierNumber == 1 => "+30.0",
            "GOES_OUT" when barrierNumber == 2 => "-30.0",
            "ENDS_IN" when barrierNumber == 1 => "+20.0",
            "ENDS_IN" when barrierNumber == 2 => "-20.0",
            "ENDS_OUT" when barrierNumber == 1 => "+35.0",
            "ENDS_OUT" when barrierNumber == 2 => "-35.0",
            _ => "+10.0"
        };
    }

    private async Task CalculateProposalAndBuyAsync()
    {
        var methodStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] CalculateProposalAndBuyAsync iniciado às {methodStartTime:HH:mm:ss.fff}");
        
        if (SelectedContractType == null || SelectedActiveSymbol == null)
        {
            _logger.LogInformation("[TIMING] CalculateProposalAndBuyAsync cancelado - dados insuficientes");
            return;
        }

        try
        {
            var requestPrepTime = DateTimeOffset.Now;
            // Prepare proposal request with minimal overhead
            var request = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = decimal.TryParse(StakeAmount?.Replace(',', '.'), NumberStyles.Float, CultureInfo.InvariantCulture, out decimal stakeVal) ? stakeVal : 0,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }

            var requestReadyTime = DateTimeOffset.Now;
            var prepDelay = (requestReadyTime - requestPrepTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] Request preparado às {requestReadyTime:HH:mm:ss.fff} (prep delay: {prepDelay}ms)");

            // Use SendFastRequestAsync for ultra-low latency when in Fast Martingale mode
            var response = IsFastMartingale ? 
                await _derivApiService.GetFastProposalAsync(request) : 
                await _derivApiService.GetProposalAsync(request);
            
            var proposalReceivedTime = DateTimeOffset.Now;
            var proposalDelay = (proposalReceivedTime - requestReadyTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] Proposta recebida às {proposalReceivedTime:HH:mm:ss.fff} (API delay: {proposalDelay}ms)");

            if (response.Error == null && response.Proposal != null)
            {
                var uiUpdateStartTime = DateTimeOffset.Now;
                // Update UI with proposal data (minimal assignments)
                CalculatedPayout = response.Proposal.Payout;
                AskPrice = response.Proposal.AskPrice;
                CurrentProposalId = response.Proposal.Id;
                CalculatedBarrier1 = response.Proposal.Barrier ?? response.Proposal.HighBarrier ?? string.Empty;
                CalculatedBarrier2 = response.Proposal.LowBarrier ?? string.Empty;

                var uiUpdateEndTime = DateTimeOffset.Now;
                var uiUpdateDelay = (uiUpdateEndTime - uiUpdateStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] UI atualizada às {uiUpdateEndTime:HH:mm:ss.fff} (UI delay: {uiUpdateDelay}ms)");

                // Execute buy immediately without additional checks for maximum speed
                if (IsConnected && AskPrice > 0 && !string.IsNullOrEmpty(CurrentProposalId))
                {
                    var buyStartTime = DateTimeOffset.Now;
                    _logger.LogInformation($"[TIMING] Iniciando compra às {buyStartTime:HH:mm:ss.fff}. ProposalId: {CurrentProposalId}, Price: {AskPrice}");
                    await ExecuteBuyCommand();
                    
                    var buyEndTime = DateTimeOffset.Now;
                    var buyDelay = (buyEndTime - buyStartTime).TotalMilliseconds;
                    var totalDelay = (buyEndTime - methodStartTime).TotalMilliseconds;
                    _logger.LogInformation($"[TIMING] Compra concluída às {buyEndTime:HH:mm:ss.fff} (buy delay: {buyDelay}ms, total: {totalDelay}ms)");
                }
                else
                {
                    _logger.LogInformation($"[TIMING] Compra cancelada - condições não atendidas. IsConnected: {IsConnected}, AskPrice: {AskPrice}, ProposalId: {CurrentProposalId}");
                }
            }
            else
            {
                _logger.LogInformation($"[TIMING] Proposta inválida recebida. Error: {response.Error?.Message}");
            }
        }
        catch (Exception ex)
        {
            var errorTime = DateTimeOffset.Now;
            var errorDelay = (errorTime - methodStartTime).TotalMilliseconds;
            _logger.LogError(ex, $"[TIMING] Erro em CalculateProposalAndBuyAsync às {errorTime:HH:mm:ss.fff} (após {errorDelay}ms)");
        }
    }

    private async void CalculateProposalAsync()
    {
        _logger.LogInformation("[DEBUG] CalculateProposalAsync chamado");
        
        if (SelectedContractType == null || SelectedActiveSymbol == null || IsCalculating)
        {
            _logger.LogInformation($"[DEBUG] Saindo: SelectedContractType={SelectedContractType?.ContractType}, SelectedActiveSymbol={SelectedActiveSymbol?.Symbol}, IsCalculating={IsCalculating}");
            return;
        }

        // Verificar se todos os campos obrigatórios estão preenchidos
        if (IsBarrier1Visible && string.IsNullOrWhiteSpace(Barrier1Value))
        {
            _logger.LogInformation($"[DEBUG] Saindo: Barrier1 obrigatória mas vazia. IsBarrier1Visible={IsBarrier1Visible}, Barrier1Value='{Barrier1Value}'");
            return;
        }
        if (IsBarrier2Visible && string.IsNullOrWhiteSpace(Barrier2Value))
        {
            _logger.LogInformation($"[DEBUG] Saindo: Barrier2 obrigatória mas vazia. IsBarrier2Visible={IsBarrier2Visible}, Barrier2Value='{Barrier2Value}'");
            return;
        }
        if (DurationValue <= 0)
        {
            _logger.LogInformation($"[DEBUG] Saindo: DurationValue inválida: {DurationValue}");
            return;
        }
        if (string.IsNullOrWhiteSpace(DurationUnit))
        {
            _logger.LogInformation($"[DEBUG] Saindo: DurationUnit vazia: '{DurationUnit}'");
            return;
        }
        var normalizedStake = StakeAmount?.Replace(',', '.');
        if (!decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal stakeValue) || stakeValue <= 0)
        {
            _logger.LogInformation($"[DEBUG] Saindo: StakeAmount inválido: '{StakeAmount}'");
            return;
        }
        
        _logger.LogInformation($"[DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType={SelectedContractType.ContractType}, Symbol={SelectedActiveSymbol.Symbol}, Stake={stakeValue}");

        try
        {
            IsCalculating = true;

            var request = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = stakeValue,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };
            
            // Só incluir LastDigitPrediction se for necessário para este tipo de contrato
            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }
            // Não definir LastDigitPrediction quando não necessário - deixar como não definido

            var response = await _derivApiService.GetProposalAsync(request);

            if (response.Error != null)
            {
                _logger.LogWarning($"Erro na proposta: {response.Error.Message}");
                CalculatedPayout = 0;
                AskPrice = 0;
                CalculatedBarrier1 = string.Empty;
                CalculatedBarrier2 = string.Empty;
            }
            else if (response.Proposal != null)
            {
                CalculatedPayout = response.Proposal.Payout;
                AskPrice = response.Proposal.AskPrice;
                CurrentProposalId = response.Proposal.Id;
                CalculatedBarrier1 = response.Proposal.Barrier ?? response.Proposal.HighBarrier ?? string.Empty;
                CalculatedBarrier2 = response.Proposal.LowBarrier ?? string.Empty;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao calcular proposta");
            CalculatedPayout = 0;
            AskPrice = 0;
            CalculatedBarrier1 = string.Empty;
            CalculatedBarrier2 = string.Empty;
        }
        finally
        {
            IsCalculating = false;
        }
    }

    // Comando para compra
    private ICommand _buyCommand;
    public ICommand BuyCommand
    {
        get
        {
            return _buyCommand ??= new RelayCommand(async () => await ExecuteBuyCommand(), CanExecuteBuy);
        }
    }

    private bool CanExecuteBuy()
    {
        var canExecute = IsConnected && 
               SelectedActiveSymbol != null && 
               SelectedContractType != null && 
               Stake > 0 && 
               DurationValue > 0 && 
               !string.IsNullOrEmpty(DurationUnit) &&
               AskPrice > 0 &&
               !string.IsNullOrEmpty(CurrentProposalId);

        if (!canExecute)
        {
            Console.WriteLine($"[CAN EXECUTE BUY DEBUG] Sessão {CurrentSessionNumber}: CanExecuteBuy = false");
            Console.WriteLine($"[CAN EXECUTE BUY DEBUG] IsConnected: {IsConnected}");
            Console.WriteLine($"[CAN EXECUTE BUY DEBUG] SelectedActiveSymbol: {SelectedActiveSymbol?.Symbol ?? "NULL"}");
            Console.WriteLine($"[CAN EXECUTE BUY DEBUG] SelectedContractType: {SelectedContractType?.ContractType ?? "NULL"}");
            Console.WriteLine($"[CAN EXECUTE BUY DEBUG] Stake: {Stake}");
            Console.WriteLine($"[CAN EXECUTE BUY DEBUG] DurationValue: {DurationValue}");
            Console.WriteLine($"[CAN EXECUTE BUY DEBUG] DurationUnit: {DurationUnit ?? "NULL"}");
            Console.WriteLine($"[CAN EXECUTE BUY DEBUG] AskPrice: {AskPrice}");
            Console.WriteLine($"[CAN EXECUTE BUY DEBUG] CurrentProposalId: {CurrentProposalId ?? "NULL"}");
        }

        return canExecute;
    }

    private async Task ExecuteBuyCommand()
    {
        try
        {
            // Log detalhado para verificar se o método está sendo chamado
            Console.WriteLine($"[EXECUTE BUY DEBUG] Sessão {CurrentSessionNumber}: ExecuteBuyCommand INICIADO");
            Console.WriteLine($"[EXECUTE BUY DEBUG] IsDualgaleEnabled: {IsDualgaleEnabled}, IsMartingaleEnabled: {IsMartingaleEnabled}");
            Console.WriteLine($"[EXECUTE BUY DEBUG] CanExecuteBuy: {CanExecuteBuy()}");
            _logger.LogInformation($"[DEBUG] Sessão {CurrentSessionNumber}: ExecuteBuyCommand - IsDualgaleEnabled: {IsDualgaleEnabled}, IsMartingaleEnabled: {IsMartingaleEnabled}, IsNoneSelected: {IsNoneSelected}");

            // Verificar se pode executar compra
            if (!CanExecuteBuy())
            {
                Console.WriteLine($"[EXECUTE BUY DEBUG] Sessão {CurrentSessionNumber}: BLOQUEADO - CanExecuteBuy retornou false");
                _logger.LogWarning($"Sessão {CurrentSessionNumber}: ExecuteBuyCommand bloqueado - CanExecuteBuy retornou false");
                return;
            }

            // Verificar se o sistema ainda está ativo
            if (!IsDualgaleEnabled && !IsMartingaleEnabled)
            {
                Console.WriteLine($"[EXECUTE BUY DEBUG] Sessão {CurrentSessionNumber}: SISTEMA INATIVO - Nenhum modo ativo");
                _logger.LogWarning($"Sessão {CurrentSessionNumber}: ExecuteBuyCommand - Nenhum modo de trading ativo");
                return;
            }

            if (IsDualgaleEnabled)
            {
                Console.WriteLine($"[EXECUTE BUY DEBUG] Sessão {CurrentSessionNumber}: Executando entradas duplas Dualgale");
                _logger.LogInformation($"[DEBUG] Sessão {CurrentSessionNumber}: Executando entradas duplas Dualgale");
                // Execute dual entries for Dualgale mode
                await ExecuteDualgaleEntries();
            }
            else
            {
                Console.WriteLine($"[EXECUTE BUY DEBUG] Sessão {CurrentSessionNumber}: Executando entrada única (Normal/Martingale)");
                _logger.LogInformation($"[DEBUG] Sessão {CurrentSessionNumber}: Executando entrada única (Normal/Martingale)");
                // Execute single entry for normal or Martingale mode
                await ExecuteSingleEntry();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[EXECUTE BUY DEBUG] Sessão {CurrentSessionNumber}: ERRO: {ex.Message}");
            _logger.LogError(ex, $"Sessão {CurrentSessionNumber}: Erro ao executar compra");
        }
    }

    private async Task ExecuteSingleEntry()
    {
        try
        {
            // Realizar a compra usando a API da Deriv
            var buyResponse = await _derivApiService.BuyContractAsync(CurrentProposalId, AskPrice);

            if (buyResponse.Error != null)
            {
                _logger.LogError("Erro na compra: {ErrorMessage}", buyResponse.Error.Message);
                return;
            }

            if (buyResponse.Buy != null)
            {
                _logger.LogInformation("Compra executada com sucesso. ContractId: {ContractId}, TransactionId: {TransactionId}",
                                      buyResponse.Buy.ContractId, buyResponse.Buy.TransactionId);

                // Add entry to Profit Table
                AddProfitTableEntry(buyResponse.Buy);

                // CRITICAL: IMMEDIATE SYNCHRONOUS HOT POOL POPULATION
                // Must populate pool NOW before any potential loss occurs
                if (IsMartingaleEnabled && IsFastMartingale)
                {
                    var preCalcStartTime = DateTimeOffset.Now;
                    _logger.LogInformation($"[TIMING] IMMEDIATE SYNC HOT POOL: Iniciando pré-cálculo SÍNCRONO após compra às {preCalcStartTime:HH:mm:ss.fff}");

                    // SYNCHRONOUS population - block until complete to ensure proposals are ready
                    try
                    {
                        await PopulateHotProposalPoolImmediate();

                        var preCalcEndTime = DateTimeOffset.Now;
                        var preCalcDuration = (preCalcEndTime - preCalcStartTime).TotalMilliseconds;
                        _logger.LogInformation($"[TIMING] IMMEDIATE SYNC HOT POOL: Pré-cálculo SÍNCRONO concluído em {preCalcDuration}ms - propostas GARANTIDAMENTE prontas");

                        lock (_poolLock)
                        {
                            _logger.LogInformation($"[DEBUG] HOT POOL STATUS: {_hotProposalPool.Count} propostas prontas nos níveis: [{string.Join(", ", _hotProposalPool.Keys)}]");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[CRITICAL] IMMEDIATE SYNC HOT POOL: ERRO CRÍTICO no pré-cálculo síncrono");
                    }
                }

                // Recalcular proposta automaticamente para manter o botão pronto
                CalculateProposalAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar entrada única");
        }
    }

    private async Task ExecuteDualgaleEntries()
    {
        try
        {
            Console.WriteLine($"[DUALGALE ENTRIES DEBUG] ==================== EXECUTANDO ENTRADAS DUPLAS SESSÃO {CurrentSessionNumber} ====================");
            Console.WriteLine($"[DUALGALE ENTRIES DEBUG] IsDualgaleEnabled: {IsDualgaleEnabled}");
            Console.WriteLine($"[DUALGALE ENTRIES DEBUG] IsDualgaleProcessing: {IsDualgaleProcessing}");
            Console.WriteLine($"[DUALGALE ENTRIES DEBUG] IsConnected: {IsConnected}");
            Console.WriteLine($"[DUALGALE ENTRIES DEBUG] StakeAmount: {StakeAmount}");
            Console.WriteLine($"[DUALGALE ENTRIES DEBUG] CurrentDualgaleLevel: {CurrentDualgaleLevel}");
            Console.WriteLine($"[DUALGALE ENTRIES DEBUG] DualgaleLostStake: {DualgaleLostStake}");
            _logger.LogInformation($"Sessão {CurrentSessionNumber}: ExecuteDualgaleEntries - Iniciando entradas duplas");

            // Verificação thread-safe para evitar race conditions
            lock (_dualgalePairsLock)
            {
                // Verificar se o sistema ainda está ativo
                if (!IsDualgaleEnabled)
                {
                    Console.WriteLine($"[DUALGALE ENTRIES DEBUG] PARADO - Dualgale foi desativado na sessão {CurrentSessionNumber}");
                    _logger.LogWarning($"Sessão {CurrentSessionNumber}: ExecuteDualgaleEntries - Dualgale foi desativado");
                    return;
                }

                // Verificar se já há uma entrada dupla em processamento
                if (IsDualgaleProcessing)
                {
                    Console.WriteLine($"[DUALGALE ENTRIES DEBUG] BLOQUEADO - Já há uma entrada dupla em processamento na sessão {CurrentSessionNumber}");
                    _logger.LogInformation($"Sessão {CurrentSessionNumber}: ExecuteDualgaleEntries - Entrada bloqueada - já há uma entrada dupla em processamento");
                    return;
                }

                // Marcar como processando dentro do lock
                IsDualgaleProcessing = true;
                Console.WriteLine($"[DUALGALE DEBUG] IsDualgaleProcessing = true (thread-safe)");
            }

            // Verificar se há contratos ativos
            var activeContracts = ProfitTableEntries.Count(entry => entry.IsActive);
            if (activeContracts > 0)
            {
                Console.WriteLine($"[DUALGALE ENTRIES DEBUG] BLOQUEADO - Há {activeContracts} contratos ativos na sessão {CurrentSessionNumber}. Aguardando expiração.");
                _logger.LogInformation($"Sessão {CurrentSessionNumber}: ExecuteDualgaleEntries - Entrada bloqueada - há {activeContracts} contratos ativos");
                IsDualgaleProcessing = false; // Reset flag
                return;
            }

            // Validar conexão
            if (!IsConnected)
            {
                Console.WriteLine($"[DUALGALE ENTRIES DEBUG] ERRO - Não conectado na sessão {CurrentSessionNumber}");
                _logger.LogError($"Sessão {CurrentSessionNumber}: ExecuteDualgaleEntries - Não conectado");
                IsDualgaleProcessing = false; // Reset flag
                return;
            }
            // Validar dados necessários
            if (SelectedActiveSymbol?.Symbol == null || SelectedContractType?.ContractType == null)
            {
                Console.WriteLine($"[DUALGALE ENTRIES DEBUG] ERRO - Dados insuficientes. Symbol: {SelectedActiveSymbol?.Symbol}, ContractType: {SelectedContractType?.ContractType}");
                _logger.LogError("ExecuteDualgaleEntries: Dados insuficientes para calcular propostas");
                IsDualgaleProcessing = false; // Reset flag
                return;
            }

            // Calculate proposals for both directions com retry logic
            ProposalResponse higherProposal = null;
            ProposalResponse lowerProposal = null;

            Console.WriteLine($"[DUALGALE ENTRIES DEBUG] Calculando propostas para CALL e PUT...");

            // Tentar calcular ambas as propostas com retry
            for (int attempt = 0; attempt < 3; attempt++)
            {
                try
                {
                    if (higherProposal?.Proposal == null)
                    {
                        Console.WriteLine($"[DUALGALE ENTRIES DEBUG] Calculando proposta CALL (tentativa {attempt + 1})...");
                        higherProposal = await CalculateProposalForContractType("CALL");
                    }

                    if (lowerProposal?.Proposal == null)
                    {
                        Console.WriteLine($"[DUALGALE ENTRIES DEBUG] Calculando proposta PUT (tentativa {attempt + 1})...");
                        lowerProposal = await CalculateProposalForContractType("PUT");
                    }

                    if (higherProposal?.Proposal != null && lowerProposal?.Proposal != null)
                    {
                        Console.WriteLine($"[DUALGALE ENTRIES DEBUG] Ambas as propostas calculadas com sucesso!");
                        break;
                    }

                    // Verificar se ainda estamos ativos antes de tentar novamente
                    if (!IsDualgaleEnabled)
                    {
                        Console.WriteLine($"[DUALGALE ENTRIES DEBUG] Dualgale desativado durante cálculo de propostas");
                        IsDualgaleProcessing = false;
                        return;
                    }

                    if (attempt < 2) // Não aguardar na última tentativa
                    {
                        Console.WriteLine($"[DUALGALE ENTRIES DEBUG] Aguardando antes da próxima tentativa...");
                        await Task.Delay(1500 + (attempt * 500));
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DUALGALE ENTRIES DEBUG] Erro na tentativa {attempt + 1}: {ex.Message}");
                    _logger.LogWarning($"Erro ao calcular propostas Dualgale (tentativa {attempt + 1}): {ex.Message}");

                    if (attempt < 2)
                    {
                        await Task.Delay(2000 + (attempt * 1000));
                    }
                }
            }

            if (higherProposal?.Proposal == null || lowerProposal?.Proposal == null)
            {
                Console.WriteLine($"[DUALGALE ENTRIES DEBUG] ERRO CRÍTICO - Falha ao calcular propostas após 3 tentativas");
                Console.WriteLine($"[DUALGALE ENTRIES DEBUG] Higher válida: {higherProposal?.Proposal != null}, Lower válida: {lowerProposal?.Proposal != null}");
                _logger.LogError("Erro crítico ao calcular propostas para entradas duplas Dualgale após 3 tentativas");
                IsDualgaleProcessing = false; // Reset flag
                return;
            }

            // Execute both entries simultaneously
            var higherTask = _derivApiService.BuyContractAsync(higherProposal.Proposal.Id, higherProposal.Proposal.AskPrice);
            var lowerTask = _derivApiService.BuyContractAsync(lowerProposal.Proposal.Id, lowerProposal.Proposal.AskPrice);

            var results = await Task.WhenAll(higherTask, lowerTask);
            var higherResponse = results[0];
            var lowerResponse = results[1];

            // Process results and create contract pair tracking
            if (higherResponse?.Buy != null && lowerResponse?.Buy != null)
            {
                var pairId = Guid.NewGuid().ToString();
                var contractPair = new DualgaleContractPair
                {
                    HigherContractId = higherResponse.Buy.ContractId.ToString(),
                    LowerContractId = lowerResponse.Buy.ContractId.ToString(),
                    HigherStake = higherResponse.Buy.BuyPrice,
                    LowerStake = lowerResponse.Buy.BuyPrice,
                    HigherPayout = higherResponse.Buy.Payout,
                    LowerPayout = lowerResponse.Buy.Payout,
                    CreatedAt = DateTime.Now
                };

                lock (_dualgalePairsLock)
                {
                    _activeDualgalePairs[pairId] = contractPair;
                }

                _logger.LogInformation("Entrada Dualgale executada. Higher: {HigherId}, Lower: {LowerId}, PairId: {PairId}",
                    higherResponse.Buy.ContractId, lowerResponse.Buy.ContractId, pairId);

                AddProfitTableEntry(higherResponse.Buy, "Higher");
                AddProfitTableEntry(lowerResponse.Buy, "Lower");

                // Atualizar atividade do watchdog
                UpdateDualgaleActivity();
            }
            else
            {
                _logger.LogError("Falha ao executar uma ou ambas entradas Dualgale");
            }

            // Recalcular proposta automaticamente
            CalculateProposalAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[DUALGALE DEBUG] ERRO CRÍTICO em ExecuteDualgaleEntries: {ex.Message}");
            _logger.LogError(ex, "Erro crítico ao executar entradas duplas Dualgale");

            // Em caso de erro crítico, tentar recuperação
            try
            {
                // Aguardar e tentar recuperação
                _ = Task.Run(async () =>
                {
                    await Task.Delay(5000);

                    if (IsDualgaleEnabled && !IsDualgaleProcessing)
                    {
                        var activeContracts = ProfitTableEntries.Count(entry => entry.IsActive);
                        if (activeContracts == 0)
                        {
                            Console.WriteLine($"[DUALGALE DEBUG] Tentando recuperação após erro em ExecuteDualgaleEntries...");
                            _logger.LogInformation("Tentando recuperação do Dualgale após erro em ExecuteDualgaleEntries");

                            // Reset para nível inicial em caso de erro crítico
                            ResetDualgale();
                            await Task.Delay(2000);

                            if (IsDualgaleEnabled)
                            {
                                await ExecuteDualgaleEntries();
                            }
                        }
                    }
                });
            }
            catch (Exception recoveryEx)
            {
                Console.WriteLine($"[DUALGALE DEBUG] ERRO na recuperação: {recoveryEx.Message}");
                _logger.LogError(recoveryEx, "Erro na recuperação do Dualgale");
            }
        }
        finally
        {
            // Sempre desmarcar o processamento no final
            IsDualgaleProcessing = false;
            Console.WriteLine($"[DUALGALE DEBUG] IsDualgaleProcessing = false (finally ExecuteDualgaleEntries)");
        }
    }

    private async Task ExecuteDualgaleEntriesWithAdjustedStakes(string losingContractType, decimal adjustedStake, decimal normalStake)
    {
        try
        {
            // Verificação thread-safe para evitar race conditions
            lock (_dualgalePairsLock)
            {
                if (IsDualgaleProcessing)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] BLOQUEADO - Já há uma entrada dupla em processamento");
                    _logger.LogInformation("Dualgale: Entrada bloqueada - já há uma entrada dupla em processamento");
                    return;
                }

                // Marcar como processando dentro do lock
                IsDualgaleProcessing = true;
            }

            // Verificar se há contratos ativos
            var activeContracts = ProfitTableEntries.Count(entry => entry.IsActive);
            if (activeContracts > 0)
            {
                Console.WriteLine($"[DUALGALE DEBUG] BLOQUEADO - Há {activeContracts} contratos ativos");
                _logger.LogInformation($"Dualgale: Entrada bloqueada - há {activeContracts} contratos ativos");
                IsDualgaleProcessing = false; // Reset flag
                return;
            }

            Console.WriteLine($"[DUALGALE DEBUG] ExecuteDualgaleEntriesWithAdjustedStakes INICIADO");
            Console.WriteLine($"[DUALGALE DEBUG] LosingContractType: {losingContractType}, AdjustedStake: {adjustedStake}, NormalStake: {normalStake}");

            // Determine stakes for each contract type
            decimal higherStake = losingContractType == "CALL" ? adjustedStake : normalStake;
            decimal lowerStake = losingContractType == "PUT" ? adjustedStake : normalStake;

            Console.WriteLine($"[DUALGALE DEBUG] Stakes calculadas - Higher: {higherStake}, Lower: {lowerStake}");

            // Calculate proposals for both directions with different stakes and retry logic
            ProposalResponse higherProposal = null;
            ProposalResponse lowerProposal = null;

            // Retry logic melhorado para proposals
            for (int retry = 0; retry < 5; retry++) // Aumentado para 5 tentativas
            {
                try
                {
                    // Verificar se ainda estamos ativos antes de cada tentativa
                    if (!IsDualgaleEnabled)
                    {
                        Console.WriteLine($"[DUALGALE DEBUG] Dualgale desativado durante retry - cancelando");
                        return;
                    }

                    if (higherProposal?.Proposal == null)
                    {
                        Console.WriteLine($"[DUALGALE DEBUG] Calculando proposta Higher com stake {higherStake}... (tentativa {retry + 1})");
                        higherProposal = await CalculateProposalForContractTypeWithStake("CALL", higherStake);
                    }
                    if (lowerProposal?.Proposal == null)
                    {
                        Console.WriteLine($"[DUALGALE DEBUG] Calculando proposta Lower com stake {lowerStake}... (tentativa {retry + 1})");
                        lowerProposal = await CalculateProposalForContractTypeWithStake("PUT", lowerStake);
                    }

                    if (higherProposal?.Proposal != null && lowerProposal?.Proposal != null)
                    {
                        break; // Ambas as propostas foram calculadas com sucesso
                    }
                }
                catch (TimeoutException ex)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Timeout na tentativa {retry + 1}: {ex.Message}");
                    _logger.LogWarning($"Timeout ao calcular propostas (tentativa {retry + 1}): {ex.Message}");

                    // Para timeouts, aguardar mais tempo antes de tentar novamente
                    if (retry < 4)
                    {
                        await Task.Delay(3000 + (retry * 1000)); // Delay progressivo
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Erro na tentativa {retry + 1}: {ex.Message}");
                    _logger.LogWarning($"Erro ao calcular propostas (tentativa {retry + 1}): {ex.Message}");

                    if (retry < 4)
                    {
                        await Task.Delay(1500 + (retry * 500)); // Delay progressivo menor para outros erros
                    }
                }
            }

            if (higherProposal?.Proposal == null || lowerProposal?.Proposal == null)
            {
                Console.WriteLine($"[DUALGALE DEBUG] ERRO: Propostas nulas após 5 tentativas! Higher: {higherProposal?.Proposal != null}, Lower: {lowerProposal?.Proposal != null}");
                _logger.LogError("Dualgale: Falha ao calcular propostas após 5 tentativas.");

                // Verificar se ainda estamos ativos antes de tentar novamente
                if (!IsDualgaleEnabled)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Dualgale desativado - não tentando novamente");
                    return;
                }

                // Implementar backoff exponencial para evitar spam de requisições
                var backoffDelay = Math.Min(10000, 2000 * (int)Math.Pow(2, Math.Min(CurrentDualgaleLevel, 3))); // Max 10 segundos
                Console.WriteLine($"[DUALGALE DEBUG] Aguardando {backoffDelay}ms antes de tentar novamente...");
                await Task.Delay(backoffDelay);

                // Verificar novamente se ainda estamos ativos após o delay
                if (!IsDualgaleEnabled)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Dualgale desativado durante backoff - cancelando");
                    return;
                }

                // Tentar novamente de forma assíncrona para não bloquear
                _ = Task.Run(async () =>
                {
                    await Task.Delay(500); // Pequeno delay adicional
                    if (IsDualgaleEnabled) // Verificação final
                    {
                        await ExecuteDualgaleEntriesWithAdjustedStakes(losingContractType, adjustedStake, normalStake);
                    }
                });
                return;
            }

            // Verificar se ambas as propostas são válidas antes de prosseguir
            if (higherProposal.Proposal.Id == null || lowerProposal.Proposal.Id == null)
            {
                Console.WriteLine($"[DUALGALE DEBUG] ERRO: IDs de propostas nulos! Higher ID: {higherProposal.Proposal.Id}, Lower ID: {lowerProposal.Proposal.Id}");
                _logger.LogError("Dualgale: IDs de propostas nulos. Tentando novamente...");

                await Task.Delay(3000);
                _ = Task.Run(async () =>
                {
                    await ExecuteDualgaleEntriesWithAdjustedStakes(losingContractType, adjustedStake, normalStake);
                });
                return;
            }

            Console.WriteLine($"[DUALGALE DEBUG] Propostas calculadas com sucesso!");
            Console.WriteLine($"[DUALGALE DEBUG] Higher ID: {higherProposal.Proposal.Id}, Lower ID: {lowerProposal.Proposal.Id}");
            _logger.LogInformation($"Dualgale: Executando entradas - Higher: {higherStake:F2}, Lower: {lowerStake:F2}");

            // Execute both entries simultaneously
            Console.WriteLine($"[DUALGALE DEBUG] Executando compras simultâneas...");
            Console.WriteLine($"[DUALGALE DEBUG] Higher - ID: {higherProposal.Proposal.Id}, Price: {higherProposal.Proposal.AskPrice}");
            Console.WriteLine($"[DUALGALE DEBUG] Lower - ID: {lowerProposal.Proposal.Id}, Price: {lowerProposal.Proposal.AskPrice}");

            var higherTask = _derivApiService.BuyContractAsync(higherProposal.Proposal.Id, higherProposal.Proposal.AskPrice);
            var lowerTask = _derivApiService.BuyContractAsync(lowerProposal.Proposal.Id, lowerProposal.Proposal.AskPrice);

            var results = await Task.WhenAll(higherTask, lowerTask);
            var higherResponse = results[0];
            var lowerResponse = results[1];

            Console.WriteLine($"[DUALGALE DEBUG] Compras executadas!");
            var higherContractId = higherResponse?.Buy?.ContractId.ToString() ?? "NULL";
            var lowerContractId = lowerResponse?.Buy?.ContractId.ToString() ?? "NULL";
            Console.WriteLine($"[DUALGALE DEBUG] Higher Response: {higherContractId}");
            Console.WriteLine($"[DUALGALE DEBUG] Lower Response: {lowerContractId}");

            // Process results and create contract pair tracking
            if (higherResponse?.Buy?.ContractId != null && lowerResponse?.Buy?.ContractId != null)
            {
                Console.WriteLine($"[DUALGALE DEBUG] AMBOS OS CONTRATOS COMPRADOS COM SUCESSO!");
                Console.WriteLine($"[DUALGALE DEBUG] Higher Contract: {higherResponse.Buy.ContractId}");
                Console.WriteLine($"[DUALGALE DEBUG] Lower Contract: {lowerResponse.Buy.ContractId}");
                var pairId = Guid.NewGuid().ToString();
                var contractPair = new DualgaleContractPair
                {
                    HigherContractId = higherResponse.Buy.ContractId.ToString(),
                    LowerContractId = lowerResponse.Buy.ContractId.ToString(),
                    HigherStake = higherResponse.Buy.BuyPrice,
                    LowerStake = lowerResponse.Buy.BuyPrice,
                    HigherPayout = higherResponse.Buy.Payout,
                    LowerPayout = lowerResponse.Buy.Payout,
                    CreatedAt = DateTime.Now
                };

                lock (_dualgalePairsLock)
                {
                    _activeDualgalePairs[pairId] = contractPair;
                }

                _logger.LogInformation("Entrada Dualgale com stakes ajustadas executada. Higher: {HigherId} (${HigherStake}), Lower: {LowerId} (${LowerStake}), PairId: {PairId}",
                    higherResponse.Buy.ContractId, higherStake, lowerResponse.Buy.ContractId, lowerStake, pairId);

                AddProfitTableEntry(higherResponse.Buy, "Higher");
                AddProfitTableEntry(lowerResponse.Buy, "Lower");

                // Atualizar atividade do watchdog
                UpdateDualgaleActivity();
            }
            else
            {
                Console.WriteLine($"[DUALGALE DEBUG] ERRO: Falha em uma ou ambas as compras!");
                Console.WriteLine($"[DUALGALE DEBUG] Higher válido: {higherResponse?.Buy?.ContractId != null}");
                Console.WriteLine($"[DUALGALE DEBUG] Lower válido: {lowerResponse?.Buy?.ContractId != null}");

                if (higherResponse?.Error != null)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Erro Higher: {higherResponse.Error.Message}");
                    _logger.LogError($"Erro na compra Higher: {higherResponse.Error.Message}");
                }
                if (lowerResponse?.Error != null)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Erro Lower: {lowerResponse.Error.Message}");
                    _logger.LogError($"Erro na compra Lower: {lowerResponse.Error.Message}");
                }

                // Verificar se ainda estamos ativos antes de tentar novamente
                if (!IsDualgaleEnabled)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Dualgale desativado - não tentando novamente");
                    return;
                }

                // Implementar limite de tentativas para evitar loops infinitos
                var maxRetries = 3;
                var currentRetry = CurrentDualgaleLevel; // Usar nível como indicador de tentativas

                if (currentRetry < maxRetries)
                {
                    var retryDelay = 3000 + (currentRetry * 1000); // Delay progressivo
                    Console.WriteLine($"[DUALGALE DEBUG] Tentativa {currentRetry + 1}/{maxRetries} - aguardando {retryDelay}ms...");
                    _logger.LogWarning($"Falha na compra Dualgale. Tentativa {currentRetry + 1}/{maxRetries} em {retryDelay}ms");

                    await Task.Delay(retryDelay);

                    // Verificar novamente se ainda estamos ativos
                    if (IsDualgaleEnabled)
                    {
                        _ = Task.Run(async () =>
                        {
                            await ExecuteDualgaleEntriesWithAdjustedStakes(losingContractType, adjustedStake, normalStake);
                        });
                    }
                }
                else
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Máximo de tentativas atingido - resetando nível");
                    _logger.LogError("Máximo de tentativas de compra Dualgale atingido - resetando para nível inicial");

                    // Reset para nível inicial após muitas falhas
                    ResetDualgale();

                    // Aguardar e tentar uma entrada inicial
                    await Task.Delay(5000);
                    if (IsDualgaleEnabled)
                    {
                        _ = Task.Run(async () => await ExecuteDualgaleEntries());
                    }
                }
            }

            // Recalcular proposta automaticamente
            CalculateProposalAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[DUALGALE DEBUG] ERRO CRÍTICO em ExecuteDualgaleEntriesWithAdjustedStakes: {ex.Message}");
            _logger.LogError(ex, "Erro crítico ao executar entradas duplas Dualgale com stakes ajustadas");

            // Em caso de erro crítico, tentar recuperação
            try
            {
                // Aguardar e tentar recuperação
                _ = Task.Run(async () =>
                {
                    await Task.Delay(5000);

                    if (IsDualgaleEnabled && !IsDualgaleProcessing)
                    {
                        var activeContracts = ProfitTableEntries.Count(entry => entry.IsActive);
                        if (activeContracts == 0)
                        {
                            Console.WriteLine($"[DUALGALE DEBUG] Tentando recuperação após erro em ExecuteDualgaleEntriesWithAdjustedStakes...");
                            _logger.LogInformation("Tentando recuperação do Dualgale após erro em ExecuteDualgaleEntriesWithAdjustedStakes");

                            // Reset para nível inicial em caso de erro crítico
                            ResetDualgale();
                            await Task.Delay(2000);

                            if (IsDualgaleEnabled)
                            {
                                await ExecuteDualgaleEntries();
                            }
                        }
                    }
                });
            }
            catch (Exception recoveryEx)
            {
                Console.WriteLine($"[DUALGALE DEBUG] ERRO na recuperação: {recoveryEx.Message}");
                _logger.LogError(recoveryEx, "Erro na recuperação do Dualgale");
            }
        }
        finally
        {
            // Sempre desmarcar o processamento no final
            IsDualgaleProcessing = false;
            Console.WriteLine($"[DUALGALE DEBUG] IsDualgaleProcessing = false (finally ExecuteDualgaleEntriesWithAdjustedStakes)");
        }
    }

    private string GetOppositeContractType(string contractType)
    {
        return contractType switch
        {
            "CALL" => "PUT",
            "PUT" => "CALL",
            "CALLE" => "PUTE", // European style
            "PUTE" => "CALLE",
            _ => null
        };
    }

    private async Task<ProposalResponse> CalculateProposalForContractType(string contractType)
    {
        try
        {
            var request = new ProposalRequest
            {
                ContractType = contractType,
                Symbol = SelectedActiveSymbol?.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = decimal.TryParse(StakeAmount?.Replace(',', '.'), NumberStyles.Float, CultureInfo.InvariantCulture, out decimal stakeVal) ? stakeVal : 0,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType?.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }

            return await _derivApiService.GetProposalAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao calcular proposta para tipo de contrato: {ContractType}", contractType);
            return null;
        }
    }

    private async Task<ProposalResponse> CalculateProposalForContractTypeWithStake(string contractType, decimal stake)
    {
        const int maxRetries = 3;

        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            try
            {
                Console.WriteLine($"[DUALGALE DEBUG] CalculateProposalForContractTypeWithStake - ContractType: {contractType}, Stake: {stake}, Tentativa: {attempt + 1}");

                // Validar parâmetros antes de criar request
                if (string.IsNullOrEmpty(contractType) || stake <= 0)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] ERRO: Parâmetros inválidos - ContractType: '{contractType}', Stake: {stake}");
                    return null;
                }

                if (SelectedActiveSymbol?.Symbol == null)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] ERRO: SelectedActiveSymbol é nulo");
                    return null;
                }

                var request = new ProposalRequest
                {
                    ContractType = contractType,
                    Symbol = SelectedActiveSymbol.Symbol,
                    Duration = DurationValue,
                    DurationUnit = DurationUnit,
                    Currency = "USD",
                    Stake = stake,
                    Barrier = IsBarrier1Visible ? Barrier1Value : null,
                    Barrier2 = IsBarrier2Visible ? Barrier2Value : null
                };

                Console.WriteLine($"[DUALGALE DEBUG] Request criado - Symbol: {request.Symbol}, Duration: {request.Duration}, DurationUnit: {request.DurationUnit}");

                if (IsDigitSelectionVisible && SelectedContractType?.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
                {
                    request.LastDigitPrediction = SelectedDigit;
                    Console.WriteLine($"[DUALGALE DEBUG] LastDigitPrediction definido: {request.LastDigitPrediction}");
                }

                Console.WriteLine($"[DUALGALE DEBUG] Chamando GetProposalAsync... (tentativa {attempt + 1})");
                var result = await _derivApiService.GetProposalAsync(request);

                if (result?.Proposal != null && !string.IsNullOrEmpty(result.Proposal.Id))
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Proposta calculada com sucesso! ID: {result.Proposal.Id}");
                    return result;
                }
                else if (result?.Error != null)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] ERRO na proposta: {result.Error.Message}");
                    _logger.LogWarning($"Erro na proposta (tentativa {attempt + 1}): {result.Error.Message}");
                }
                else
                {
                    Console.WriteLine($"[DUALGALE DEBUG] ERRO: Proposta retornou nula ou inválida!");
                }

                // Se não é a última tentativa, aguardar antes de tentar novamente
                if (attempt < maxRetries - 1)
                {
                    var delay = 1000 + (attempt * 500); // Delay progressivo
                    Console.WriteLine($"[DUALGALE DEBUG] Aguardando {delay}ms antes da próxima tentativa...");
                    await Task.Delay(delay);

                    // Verificar se ainda estamos ativos
                    if (!IsDualgaleEnabled)
                    {
                        Console.WriteLine($"[DUALGALE DEBUG] Dualgale desativado durante retry - cancelando");
                        return null;
                    }
                }
            }
            catch (TimeoutException ex)
            {
                Console.WriteLine($"[DUALGALE DEBUG] TIMEOUT em CalculateProposalForContractTypeWithStake (tentativa {attempt + 1}): {ex.Message}");
                _logger.LogWarning($"Timeout ao calcular proposta para {contractType} com stake {stake} (tentativa {attempt + 1}): {ex.Message}");

                if (attempt < maxRetries - 1)
                {
                    await Task.Delay(2000 + (attempt * 1000)); // Delay maior para timeouts
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DUALGALE DEBUG] EXCEÇÃO em CalculateProposalForContractTypeWithStake (tentativa {attempt + 1}): {ex.Message}");
                _logger.LogError(ex, "Erro ao calcular proposta para tipo de contrato: {ContractType} com stake: {Stake} (tentativa {Attempt})", contractType, stake, attempt + 1);

                if (attempt < maxRetries - 1)
                {
                    await Task.Delay(1500 + (attempt * 500));
                }
            }
        }

        Console.WriteLine($"[DUALGALE DEBUG] FALHA TOTAL: Não foi possível calcular proposta após {maxRetries} tentativas");
        _logger.LogError($"Falha total ao calcular proposta para {contractType} com stake {stake} após {maxRetries} tentativas");
        return null;
    }

    // Métodos do Martingale e Dualgale
    private void CalculateNextStake()
    {
        if (!IsMartingaleEnabled && !IsDualgaleEnabled)
        {
            var normalizedStake = StakeAmount?.Replace(',', '.');
            NextStakeAmount = decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal currentStake) ? currentStake : 0;
            return;
        }

        if (InitialStakeAmount == 0)
        {
            var normalizedStake = StakeAmount?.Replace(',', '.');
            if (decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal initialStake))
            {
                InitialStakeAmount = initialStake;
                Console.WriteLine($"[STAKE DEBUG] InitialStakeAmount definido como: {InitialStakeAmount:F2}");
            }
        }
        else
        {
            Console.WriteLine($"[STAKE DEBUG] InitialStakeAmount já definido: {InitialStakeAmount:F2}");
        }

        if (IsMartingaleEnabled)
        {
            if (CurrentMartingaleLevel == 0)
            {
                NextStakeAmount = InitialStakeAmount;
            }
            else
            {
                NextStakeAmount = Math.Round(InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, CurrentMartingaleLevel), 2);
            }
        }
        else if (IsDualgaleEnabled)
        {
            if (CurrentDualgaleLevel == 0 || DualgaleLostStake == 0)
            {
                NextStakeAmount = InitialStakeAmount;
            }
            else
            {
                // Calculate stake to recover the specified percentage of lost amount
                // Formula: next_stake = (%_recover * lost_stake) / expected_payout_multiplier
                var recoverAmount = DualgaleLostStake * (DualgaleRecoverPercent / 100m);

                // Use a conservative payout estimate (typically around 1.8-1.95 for binary options)
                // This will be refined when we have actual payout data
                var estimatedPayoutMultiplier = 1.85m; // Conservative estimate
                NextStakeAmount = Math.Round(recoverAmount / estimatedPayoutMultiplier, 2);

                // Apply factor for progression
                NextStakeAmount = Math.Round(NextStakeAmount * DualgaleFactor, 2);
            }
        }
    }

    // IMMEDIATE HOT PROPOSAL POOL - Synchronous population for instant execution
    private async Task PopulateHotProposalPoolImmediate()
    {
        if (_isPoolPopulating || !IsFastMartingale || !IsMartingaleEnabled) return;
        
        _isPoolPopulating = true;
        var poolStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] HOT POOL IMMEDIATE: Starting AGGRESSIVE population at {poolStartTime:HH:mm:ss.fff}");
        
        try
        {
            // Validate required fields
            if (SelectedContractType?.ContractType == null || SelectedActiveSymbol?.Symbol == null)
            {
                _logger.LogWarning("[DEBUG] HOT POOL IMMEDIATE: Missing required fields - canceling population");
                return;
            }
            
            // AGGRESSIVE POPULATION: Clear and rebuild entire pool for freshness
            lock (_poolLock)
            {
                _hotProposalPool.Clear();
                _logger.LogInformation("[DEBUG] HOT POOL IMMEDIATE: Pool cleared for complete rebuild");
            }
            
            // PRE-EMPTIVE POPULATION: Calculate for ALL possible next levels (1-5)
            var populationTasks = new List<Task>();
            
            for (int level = 1; level <= 5; level++)
            {
                var levelTask = Task.Run(async () =>
                {
                    try
                    {
                        var levelStartTime = DateTimeOffset.Now;
                        var futureStake = Math.Round(InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, level), 2);
                        
                        var request = new ProposalRequest
                        {
                            ContractType = SelectedContractType.ContractType,
                            Symbol = SelectedActiveSymbol.Symbol,
                            Duration = DurationValue,
                            DurationUnit = DurationUnit,
                            Currency = "USD",
                            Stake = futureStake,
                            Barrier = IsBarrier1Visible ? Barrier1Value : null,
                            Barrier2 = IsBarrier2Visible ? Barrier2Value : null
                        };

                        if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
                        {
                            request.LastDigitPrediction = SelectedDigit;
                        }
                        
                        // FAST PROPOSAL REQUEST: Use high-speed API
                        var response = await _derivApiService.GetFastProposalAsync(request);
                        
                        if (response?.Error == null && response?.Proposal != null)
                        {
                            lock (_poolLock)
                            {
                                _hotProposalPool[level] = response;
                            }
                            
                            var levelEndTime = DateTimeOffset.Now;
                            var levelDelay = (levelEndTime - levelStartTime).TotalMilliseconds;
                            _logger.LogInformation($"[TIMING] HOT POOL AGGRESSIVE: Level {level} populated in {levelDelay}ms. Stake: {futureStake:F2}, ProposalId: {response.Proposal.Id}");
                        }
                        else
                        {
                            _logger.LogError($"[DEBUG] HOT POOL AGGRESSIVE: Error populating level {level}: {response?.Error?.Message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"[DEBUG] HOT POOL AGGRESSIVE: Exception in level {level}");
                    }
                });
                
                populationTasks.Add(levelTask);
            }
            
            // PARALLEL EXECUTION: Wait for all levels to complete
            await Task.WhenAll(populationTasks);
            
            var poolEndTime = DateTimeOffset.Now;
            var totalPoolTime = (poolEndTime - poolStartTime).TotalMilliseconds;
            
            lock (_poolLock)
            {
                _logger.LogInformation($"[TIMING] HOT POOL AGGRESSIVE: Population completed in {totalPoolTime}ms. Pool contains {_hotProposalPool.Count} proposals GUARANTEED ready. Levels: [{string.Join(", ", _hotProposalPool.Keys)}]");
                
                // VALIDATION: Ensure we have at least the next 2 levels ready
                var nextLevel = CurrentMartingaleLevel + 1;
                var hasNextLevel = _hotProposalPool.ContainsKey(nextLevel);
                var hasSecondLevel = _hotProposalPool.ContainsKey(nextLevel + 1);
                
                if (!hasNextLevel || !hasSecondLevel)
                {
                    _logger.LogWarning($"[DEBUG] HOT POOL VALIDATION: Missing critical levels. Next: {hasNextLevel}, Second: {hasSecondLevel}. Current: {CurrentMartingaleLevel}");
                }
                else
                {
                    _logger.LogInformation($"[DEBUG] HOT POOL VALIDATION: Critical levels ready. Pool is OPTIMAL for instant execution.");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[DEBUG] HOT POOL IMMEDIATE: Critical error in aggressive population");
        }
        finally
        {
            _isPoolPopulating = false;
        }
    }

    // LEGACY: Async hot proposal pool for backward compatibility
    private async Task PopulateHotProposalPool()
    {
        // Redirect to immediate synchronous version for consistency
        await PopulateHotProposalPoolImmediate();
    }
    
    // Replenish used proposal in the pool (optimized for background execution)
    private async Task ReplenishHotProposal(int level)
    {
        try
        {
            if (!IsFastMartingale || !IsMartingaleEnabled) return;
            
            var replenishStartTime = DateTimeOffset.Now;
            var futureStake = Math.Round(InitialStakeAmount * (decimal)Math.Pow((double)MartingaleFactor, level), 2);
            
            var request = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = futureStake,
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                request.LastDigitPrediction = SelectedDigit;
            }
            
            var response = await _derivApiService.GetFastProposalAsync(request);
            
            if (response?.Error == null && response?.Proposal != null)
            {
                lock (_poolLock)
                {
                    _hotProposalPool[level] = response;
                }
                
                var replenishEndTime = DateTimeOffset.Now;
                var replenishDelay = (replenishEndTime - replenishStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] HOT POOL REPLENISH: Nível {level} reabastecido em {replenishDelay}ms. ProposalId: {response.Proposal.Id}");
            }
            else
            {
                _logger.LogError($"[DEBUG] HOT POOL REPLENISH: Erro ao reabastecer nível {level}: {response?.Error?.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[DEBUG] HOT POOL REPLENISH: Erro ao reabastecer nível {level}");
        }
    }

    // ULTRA-FAST LOSS EXECUTION: Zero-overhead processing for true sub-100ms execution
    private void OnContractLossUltraFast()
    {
        var startTime = DateTimeOffset.Now;
        
        // VALIDATION: Ultra-fast checks with minimal overhead
        if (!IsMartingaleEnabled || CurrentMartingaleLevel >= MartingaleLevel) 
        {
            if (CurrentMartingaleLevel >= MartingaleLevel)
            {
                ResetMartingale();
            }
            return;
        }

        // INSTANT STATE UPDATE: No property change notifications during critical path
        var previousLevel = CurrentMartingaleLevel;
        CurrentMartingaleLevel++;
        var newStake = NextStakeAmount.ToString("F2");
        _stakeAmount = newStake; // Direct field access to avoid property overhead
        
        if (!IsFastMartingale)
        {
            // Update UI for manual mode after state change
            Application.Current.Dispatcher.BeginInvoke(new Action(() => {
                OnPropertyChanged(nameof(StakeAmount));
            }), System.Windows.Threading.DispatcherPriority.Background);
            return;
        }

        var stateUpdateTime = DateTimeOffset.Now;
        var stateDelay = (stateUpdateTime - startTime).TotalMilliseconds;
        _logger.LogInformation($"[TIMING] ULTRA-FAST: State updated in {stateDelay}ms. Level: {previousLevel} → {CurrentMartingaleLevel}, Stake: {newStake}");

        // ZERO-OVERHEAD POOL ACCESS: Direct dictionary access without try-catch overhead
        ProposalResponse hotProposal = null;
        bool proposalFound = false;
        
        // Ultra-fast lock-free access when possible
        if (_hotProposalPool.ContainsKey(CurrentMartingaleLevel))
        {
            lock (_poolLock)
            {
                if (_hotProposalPool.TryGetValue(CurrentMartingaleLevel, out hotProposal))
                {
                    _hotProposalPool.Remove(CurrentMartingaleLevel);
                    proposalFound = true;
                }
            }
        }
        
        var retrievalTime = DateTimeOffset.Now;
        var retrievalDelay = (retrievalTime - stateUpdateTime).TotalMilliseconds;
        
        if (proposalFound && hotProposal?.Error == null && hotProposal?.Proposal != null)
        {
            _logger.LogInformation($"[TIMING] ULTRA-FAST: Hot proposal retrieved in {retrievalDelay}ms. ProposalId: {hotProposal.Proposal.Id}");
            
            // DIRECT WEBSOCKET EXECUTION: Bypass all intermediate layers
            var proposalId = hotProposal.Proposal.Id;
            var askPrice = hotProposal.Proposal.AskPrice;
            
            // IMMEDIATE WEBSOCKET SEND: Direct JSON construction and send
            var buyJson = $"{{\"buy\":\"{proposalId}\",\"price\":{askPrice},\"subscribe\":1}}";
            
            try
            {
                // Direct WebSocket access through service
                _derivApiService.SendDirectBuyCommand(buyJson);
                
                var executionTime = DateTimeOffset.Now;
                var totalTime = (executionTime - startTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] ULTRA-FAST: Buy command sent in {totalTime}ms total - TRUE SUB-100MS EXECUTION");
                
                // MINIMAL UI UPDATE: Deferred to background with minimal data
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    CurrentProposalId = proposalId;
                    AskPrice = askPrice;
                    CalculatedPayout = hotProposal.Proposal.Payout;
                    OnPropertyChanged(nameof(StakeAmount)); // Update UI after execution
                }), System.Windows.Threading.DispatcherPriority.Background);
                
                // BACKGROUND REPLENISHMENT: Fire-and-forget
                _ = Task.Run(() => ReplenishHotProposal(CurrentMartingaleLevel));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[CRITICAL] ULTRA-FAST: Direct WebSocket send failed - falling back to standard method");
                _derivApiService.BuyContractImmediateAsync(proposalId, askPrice, null);
            }
        }
        else
        {
            var emergencyTime = DateTimeOffset.Now;
            var emergencyDelay = (emergencyTime - startTime).TotalMilliseconds;
            _logger.LogError($"[TIMING] ULTRA-FAST EMERGENCY: No hot proposal available in {emergencyDelay}ms. Level: {CurrentMartingaleLevel}");
            
            // EMERGENCY FALLBACK: Direct execution with minimal overhead
            var emergencyRequest = new ProposalRequest
            {
                ContractType = SelectedContractType.ContractType,
                Symbol = SelectedActiveSymbol.Symbol,
                Duration = DurationValue,
                DurationUnit = DurationUnit,
                Currency = "USD",
                Stake = decimal.Parse(newStake),
                Barrier = IsBarrier1Visible ? Barrier1Value : null,
                Barrier2 = IsBarrier2Visible ? Barrier2Value : null
            };

            if (IsDigitSelectionVisible && SelectedContractType.LastDigitRange != null && SelectedContractType.LastDigitRange.Any())
            {
                emergencyRequest.LastDigitPrediction = SelectedDigit;
            }
            
            _derivApiService.BuyInstantMarketAsync(emergencyRequest, null);
            
            var emergencyExecutionTime = DateTimeOffset.Now;
            var emergencyTotalTime = (emergencyExecutionTime - startTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] ULTRA-FAST EMERGENCY: Fallback sent in {emergencyTotalTime}ms");
            
            // Update UI after emergency execution
            Application.Current.Dispatcher.BeginInvoke(new Action(() => {
                OnPropertyChanged(nameof(StakeAmount));
            }), System.Windows.Threading.DispatcherPriority.Background);
            
            // EMERGENCY POOL REBUILD: Background
            _ = Task.Run(() => PopulateHotProposalPoolImmediate());
        }
    }

    // IMMEDIATE LOSS EXECUTION: Zero-delay processing for fast martingale
    private void OnContractLossImmediate()
    {
        // Redirect to ultra-fast implementation for maximum performance
        OnContractLossUltraFast();
    }

    // LEGACY: Keep for backward compatibility
    public void OnContractLoss()
    {
        OnContractLossImmediate();
    }

    public void OnContractWin()
    {
        if (!IsMartingaleEnabled) return;
        
        // Reset to initial stake after a win
        ResetMartingale();
    }

    private void ResetMartingale()
    {
        CurrentMartingaleLevel = 0;
        StakeAmount = InitialStakeAmount.ToString("F2");
        CalculateNextStake();
    }

    // Override do StakeAmount para integrar com Martingale
    private string _stakeAmount = "1.00";
    public string StakeAmount
    {
        get => _stakeAmount;
        set
        {
            // Capturar valor original na primeira vez que é definido
            if (string.IsNullOrEmpty(_originalStakeValue) && !string.IsNullOrEmpty(value))
            {
                _originalStakeValue = value;
                Console.WriteLine($"[STAKE ORIGINAL] Valor original capturado: {_originalStakeValue}");
            }

            _stakeAmount = value;
            OnPropertyChanged();

            // Usar o mesmo parsing que o DecimalConverter para consistência
            var normalizedValue = value?.Replace(',', '.');
            if (decimal.TryParse(normalizedValue, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal stakeValue))
            {
                _stake = stakeValue;
                OnPropertyChanged(nameof(Stake));

                // Update initial stake if martingale is enabled and this is a manual change
                if (IsMartingaleEnabled && CurrentMartingaleLevel == 0)
                {
                    InitialStakeAmount = stakeValue;
                }
            }

            CalculateNextStake();
            CalculateProposalAsync();
        }
    }

    // Profit Table Methods
    private void AddProfitTableEntry(BuyContract buyContract, string contractTypeOverride = null)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            // Atualizar estatísticas globais
            var currentLevel = IsMartingaleEnabled ? CurrentMartingaleLevel : (IsDualgaleEnabled ? CurrentDualgaleLevel : 0);
            ProfitTableEntry.UpdateGlobalStats(buyContract.BuyPrice, currentLevel, TotalProfit);

            // Atualizar propriedades locais
            SessionCount = ProfitTableEntry.GlobalSessionCount;
            MaxStakeUsed = ProfitTableEntry.GlobalMaxStakeUsed;
            MaxStakeSession = ProfitTableEntry.GlobalMaxStakeSession;
            MaxLevel = ProfitTableEntry.GlobalMaxLevel;

            var entry = new ProfitTableEntry
            {
                RefId = buyContract.ContractId.ToString(),
                Contract = contractTypeOverride ?? GetContractTypeDisplayName(),
                Duration = GetDurationDisplayText(),
                EntrySpot = DateTimeOffset.FromUnixTimeSeconds(buyContract.PurchaseTime).DateTime,
                Stake = buyContract.BuyPrice,
                Payout = buyContract.Payout,
                EntryPrice = CurrentTickPrice,
                IsActive = true,
                SessionNumber = SessionCount,
                MaxStakeUsed = MaxStakeUsed,
                MaxStakeSession = MaxStakeSession,
                MaxLevel = MaxLevel
            };

            ProfitTableEntries.Insert(0, entry); // Add to top of list

            // Keep only last 50 entries to avoid memory issues
            while (ProfitTableEntries.Count > 50)
            {
                ProfitTableEntries.RemoveAt(ProfitTableEntries.Count - 1);
            }

            // Update total profit
            UpdateTotalProfit();
        });
    }

    private string GetContractTypeDisplayName()
    {
        if (SelectedContractType != null)
        {
            return SelectedContractType.ContractDisplay;
        }
        return "Unknown";
    }

    private string GetDurationDisplayText()
    {
        if (!string.IsNullOrEmpty(DurationValue.ToString()) && !string.IsNullOrEmpty(DurationUnit))
        {
            var unitDisplay = DurationUnit switch
            {
                "t" => "ticks",
                "s" => "sec",
                "m" => "min",
                "h" => "hr",
                "d" => "day",
                _ => DurationUnit
            };
            return $"{DurationValue} {unitDisplay}";
        }
        return "---";
    }

    private void UpdateProfitTableEntry(string contractId, decimal currentPrice, decimal? profit = null, bool? isFinished = null)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var entry = ProfitTableEntries.FirstOrDefault(e => e.RefId == contractId);
            if (entry != null)
            {
                entry.CurrentPrice = currentPrice;

                if (profit.HasValue)
                {
                    entry.TotalProfitLoss = profit.Value;
                }

                if (isFinished.HasValue && isFinished.Value)
                {
                    entry.IsActive = false;
                    entry.ExitSpot = DateTime.Now;
                    entry.ExitPrice = currentPrice;
                }

                // Update total profit when entry changes
                UpdateTotalProfit();
            }
        });
    }

    private void ClearProfitTableKeepingStats()
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            // Limpar apenas os registros da tabela
            ProfitTableEntries.Clear();

            // Resetar apenas o SessionProfit (TotalProfit é mantido acumulativo)
            SessionProfit = 0;

            // IMPORTANTE: NÃO resetar as estatísticas globais nem locais
            // Elas devem ser mantidas acumuladas entre sessões
            // Removido: ProfitTableEntry.ResetGlobalStats();
            // Removido: SessionCount = 0; MaxStakeUsed = 0; MaxStakeSession = 0; MaxLevel = 0;

            Console.WriteLine($"[CLEAR STATS] Tabela limpa mantendo estatísticas. Sessão: {CurrentSessionNumber}/{MaxSessions}");
            Console.WriteLine($"[CLEAR STATS] Estatísticas mantidas - SessionCount: {SessionCount}, MaxStakeUsed: {MaxStakeUsed:F2}, MaxLevel: {MaxLevel}");
            _logger.LogInformation($"Profit Table limpa para nova sessão mantendo estatísticas. Sessão atual: {CurrentSessionNumber}/{MaxSessions}");
        });
    }

    private async void StartNewSession()
    {
        try
        {
            Console.WriteLine($"[START NEW SESSION] ==================== INICIANDO SESSÃO {CurrentSessionNumber}/{MaxSessions} ====================");
            Console.WriteLine($"[START NEW SESSION] IsDualgaleEnabled: {IsDualgaleEnabled}");
            Console.WriteLine($"[START NEW SESSION] IsMartingaleEnabled: {IsMartingaleEnabled}");
            Console.WriteLine($"[START NEW SESSION] IsConnected: {IsConnected}");
            Console.WriteLine($"[START NEW SESSION] StakeAmount: {StakeAmount}");
            Console.WriteLine($"[START NEW SESSION] _originalStakeValue: {_originalStakeValue}");
            Console.WriteLine($"[START NEW SESSION] CurrentDualgaleLevel: {CurrentDualgaleLevel}");
            Console.WriteLine($"[START NEW SESSION] DualgaleLostStake: {DualgaleLostStake}");
            Console.WriteLine($"[START NEW SESSION] IsDualgaleProcessing: {IsDualgaleProcessing}");
            Console.WriteLine($"[START NEW SESSION] CanExecuteBuy: {CanExecuteBuy()}");
            _logger.LogInformation($"Iniciando nova sessão automática {CurrentSessionNumber}/{MaxSessions}");

            // Aguardar um momento para garantir que a interface foi atualizada
            await Task.Delay(3000);

            // Verificar se ainda estamos no modo correto antes de executar
            if (!IsMartingaleEnabled && !IsDualgaleEnabled)
            {
                Console.WriteLine($"[START NEW SESSION] ERRO - Modos desativados! Martingale: {IsMartingaleEnabled}, Dualgale: {IsDualgaleEnabled}");
                _logger.LogWarning("Modos desativados durante inicialização da nova sessão. Cancelando.");
                return;
            }

            Console.WriteLine($"[START NEW SESSION] Modos ativos - Martingale: {IsMartingaleEnabled}, Dualgale: {IsDualgaleEnabled}");

            // Verificar condições básicas antes de executar
            if (!IsConnected)
            {
                Console.WriteLine($"[START NEW SESSION] ERRO: Não conectado na sessão {CurrentSessionNumber}");
                _logger.LogError($"Tentativa de iniciar sessão {CurrentSessionNumber} sem conexão");
                return;
            }

            // Verificar se StakeAmount é válido
            var normalizedStake = StakeAmount?.Replace(',', '.');
            if (!decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal stakeValue) || stakeValue <= 0)
            {
                Console.WriteLine($"[START NEW SESSION] ERRO: StakeAmount inválido ({StakeAmount}) na sessão {CurrentSessionNumber}");
                _logger.LogError($"Tentativa de iniciar sessão {CurrentSessionNumber} com StakeAmount inválido: {StakeAmount}");
                return;
            }

            // Executar entrada automática baseada no modo ativo
            if (IsMartingaleEnabled)
            {
                Console.WriteLine($"[START NEW SESSION] Executando Martingale...");
                _logger.LogInformation("Executando entrada automática Martingale para nova sessão");
                await ExecuteBuyCommand();
                Console.WriteLine($"[START NEW SESSION] Martingale executado para sessão {CurrentSessionNumber}");
            }
            else if (IsDualgaleEnabled)
            {
                Console.WriteLine($"[START NEW SESSION] Executando Dualgale...");
                _logger.LogInformation("Executando entrada automática Dualgale para nova sessão");
                await ExecuteBuyCommand();
                Console.WriteLine($"[START NEW SESSION] Dualgale executado para sessão {CurrentSessionNumber}");
            }
            else
            {
                Console.WriteLine($"[START NEW SESSION] ERRO - Nenhum modo ativo!");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[START NEW SESSION] ERRO CRÍTICO na sessão {CurrentSessionNumber}: {ex.Message}");
            Console.WriteLine($"[START NEW SESSION] Stack trace: {ex.StackTrace}");
            _logger.LogError(ex, $"Erro ao iniciar nova sessão automática {CurrentSessionNumber}");
        }
    }

    private void UpdateActiveProfitTableEntries(decimal currentPrice)
    {
        foreach (var entry in ProfitTableEntries.Where(e => e.IsActive))
        {
            entry.CurrentPrice = currentPrice;
        }
    }

    private void OnContractFinished(string contractId, decimal profit, decimal exitPrice, DateTime exitTime)
    {
        Console.WriteLine($"[DUALGALE DEBUG] OnContractFinished chamado para contrato {contractId}, profit: {profit}, IsDualgaleEnabled: {IsDualgaleEnabled}");
        _logger.LogInformation($"[DEBUG] OnContractFinished chamado para contrato {contractId}, profit: {profit}, IsDualgaleEnabled: {IsDualgaleEnabled}");

        Application.Current.Dispatcher.Invoke(() =>
        {
            var entry = ProfitTableEntries.FirstOrDefault(e => e.RefId == contractId && e.IsActive);
            if (entry != null)
            {
                entry.TotalProfitLoss = profit;
                entry.ExitPrice = exitPrice;
                entry.ExitSpot = exitTime;
                entry.IsActive = false;

                Console.WriteLine($"[DUALGALE DEBUG] Profit Table updated for contract {contractId}: Profit={profit}");
                _logger.LogInformation($"Profit Table updated for contract {contractId}: Profit={profit}, ExitPrice={exitPrice}");

                // Update apenas session profit (TotalProfit é atualizado apenas quando sessão termina)
                UpdateTotalProfit();

                // Handle Dualgale logic when contract finishes
                if (IsDualgaleEnabled)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Chamando HandleDualgaleContractFinished para contrato {contractId}");
                    _logger.LogInformation($"[DEBUG] Chamando HandleDualgaleContractFinished para contrato {contractId}");

                    try
                    {
                        HandleDualgaleContractFinished(contractId, profit);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[DUALGALE DEBUG] ERRO em HandleDualgaleContractFinished: {ex.Message}");
                        _logger.LogError(ex, $"Erro crítico em HandleDualgaleContractFinished para contrato {contractId}");

                        // Em caso de erro crítico, tentar recuperar o sistema
                        _ = Task.Run(async () =>
                        {
                            await Task.Delay(5000); // Aguardar 5 segundos

                            if (IsDualgaleEnabled && !IsDualgaleProcessing)
                            {
                                var activeContracts = ProfitTableEntries.Count(entry => entry.IsActive);
                                if (activeContracts == 0)
                                {
                                    Console.WriteLine($"[DUALGALE DEBUG] Tentando recuperação automática após erro...");
                                    _logger.LogInformation("Tentando recuperação automática do Dualgale após erro");

                                    try
                                    {
                                        await ExecuteDualgaleEntries();
                                    }
                                    catch (Exception recoveryEx)
                                    {
                                        Console.WriteLine($"[DUALGALE DEBUG] ERRO na recuperação: {recoveryEx.Message}");
                                        _logger.LogError(recoveryEx, "Erro na recuperação automática do Dualgale");
                                    }
                                }
                            }
                        });
                    }
                }
                else
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Dualgale não está ativo, pulando lógica automática");
                    _logger.LogInformation($"[DEBUG] Dualgale não está ativo, pulando lógica automática");
                }
            }
            else
            {
                Console.WriteLine($"[DUALGALE DEBUG] Entrada não encontrada na Profit Table para contrato {contractId}");
                _logger.LogWarning($"[DEBUG] Entrada não encontrada na Profit Table para contrato {contractId}");
            }
        });
    }

    private void HandleDualgaleContractFinished(string contractId, decimal profit)
    {
        try
        {
            Console.WriteLine($"[DUALGALE DEBUG] HandleDualgaleContractFinished iniciado para contrato {contractId}");

            lock (_dualgalePairsLock)
            {
                Console.WriteLine($"[DUALGALE DEBUG] Procurando par para contrato {contractId}. Pares ativos: {_activeDualgalePairs.Count}");

                // Find the contract pair that contains this contract
                var pair = _activeDualgalePairs.Values.FirstOrDefault(p =>
                    p.HigherContractId == contractId || p.LowerContractId == contractId);

                if (pair != null)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Par encontrado! Higher: {pair.HigherContractId}, Lower: {pair.LowerContractId}");

                    // Update the finished contract
                    if (pair.HigherContractId == contractId)
                    {
                        pair.HigherFinished = true;
                        pair.HigherProfit = profit;
                        Console.WriteLine($"[DUALGALE DEBUG] Higher finalizado: {profit}");
                    }
                    else if (pair.LowerContractId == contractId)
                    {
                        pair.LowerFinished = true;
                        pair.LowerProfit = profit;
                        Console.WriteLine($"[DUALGALE DEBUG] Lower finalizado: {profit}");
                    }

                    Console.WriteLine($"[DUALGALE DEBUG] Status do par: HigherFinished={pair.HigherFinished}, LowerFinished={pair.LowerFinished}");
                    _logger.LogInformation($"Dualgale: Contrato {contractId} finalizado com profit {profit}");

                    // Atualizar atividade do watchdog
                    UpdateDualgaleActivity();

                    // Check if both contracts in the pair are finished
                    if (pair.BothFinished)
                    {
                        Console.WriteLine($"[DUALGALE DEBUG] AMBOS CONTRATOS FINALIZADOS! Chamando ProcessCompletedDualgalePair");
                        _logger.LogInformation($"Dualgale: Par completo finalizado. Higher: {pair.HigherProfit}, Lower: {pair.LowerProfit}");

                        // Process the completed pair
                        ProcessCompletedDualgalePair(pair);

                        // Remove the completed pair from tracking
                        var pairToRemove = _activeDualgalePairs.FirstOrDefault(kvp => kvp.Value == pair);
                        if (pairToRemove.Key != null)
                        {
                            _activeDualgalePairs.Remove(pairToRemove.Key);
                            Console.WriteLine($"[DUALGALE DEBUG] Par removido da lista de ativos");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"[DUALGALE DEBUG] Aguardando o outro contrato do par finalizar...");
                    }
                }
                else
                {
                    Console.WriteLine($"[DUALGALE DEBUG] ERRO: Par não encontrado para contrato {contractId}!");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[DUALGALE DEBUG] ERRO em HandleDualgaleContractFinished: {ex.Message}");
            _logger.LogError(ex, "Erro ao processar lógica Dualgale");
        }
    }

    private async void ProcessCompletedDualgalePair(DualgaleContractPair pair)
    {
        try
        {
            Console.WriteLine($"[DUALGALE DEBUG] ProcessCompletedDualgalePair INICIADO");

            // Verificação thread-safe para evitar race conditions
            lock (_dualgalePairsLock)
            {
                if (IsDualgaleProcessing)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] BLOQUEADO - Já há uma entrada dupla em processamento");
                    _logger.LogInformation("Dualgale: Processamento de par completado bloqueado - já há uma entrada dupla em processamento");
                    return;
                }

                // Verificar se ainda estamos ativos
                if (!IsDualgaleEnabled)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] PARADO - Dualgale foi desativado");
                    _logger.LogInformation("Dualgale: Processamento cancelado - sistema desativado");
                    return;
                }
            }

            // Determine which contract lost and calculate recovery
            var totalProfit = (pair.HigherProfit ?? 0) + (pair.LowerProfit ?? 0);
            var lostStake = pair.HigherProfit < 0 ? pair.HigherStake : pair.LowerStake;
            var winningPayout = pair.HigherProfit > 0 ? pair.HigherPayout : pair.LowerPayout;

            Console.WriteLine($"[DUALGALE DEBUG] Profit total: {totalProfit}, Stake perdida: {lostStake}");
            Console.WriteLine($"[DUALGALE DEBUG] CurrentDualgaleLevel: {CurrentDualgaleLevel}, DualgaleLevel: {DualgaleLevel}, TotalProfit: {TotalProfit}");

            _logger.LogInformation($"Dualgale: Par finalizado. Profit total: {totalProfit}, Stake perdida: {lostStake}");
            _logger.LogInformation($"[DEBUG] CurrentDualgaleLevel: {CurrentDualgaleLevel}, DualgaleLevel: {DualgaleLevel}, TotalProfit: {TotalProfit}");

            // Check if we should continue (not reached max level)
            if (CurrentDualgaleLevel >= DualgaleLevel)
            {
                Console.WriteLine($"[DUALGALE DEBUG] Nível máximo atingido ({CurrentDualgaleLevel} >= {DualgaleLevel}). Resetando e fazendo nova entrada inicial.");
                _logger.LogInformation("Dualgale: Nível máximo atingido. Resetando e fazendo nova entrada inicial.");
                ResetDualgale();

                // Aguardar um momento e fazer nova entrada inicial
                await Task.Delay(2000);

                // Verificar se ainda está ativo antes de continuar
                if (!IsDualgaleEnabled)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Dualgale foi desativado durante reset. Parando.");
                    return;
                }

                // Fazer nova entrada inicial
                Console.WriteLine($"[DUALGALE DEBUG] Executando nova entrada inicial após reset...");
                await ExecuteDualgaleEntries();
                return;
            }

            // Não parar por lucro positivo - deixar o sistema de Take Profit controlar
            // if (TotalProfit > 0)
            // {
            //     Console.WriteLine($"[DUALGALE DEBUG] Lucro positivo atingido ({TotalProfit}). Parando ciclo.");
            //     _logger.LogInformation("Dualgale: Lucro positivo atingido. Parando ciclo automático.");
            //     ResetDualgale();
            //     return;
            // }

            // Increment level and calculate next stake
            CurrentDualgaleLevel++;
            Console.WriteLine($"[DUALGALE DEBUG] Incrementando nível para: {CurrentDualgaleLevel}");

            // Calculate the stake needed to recover the specified percentage
            var recoverAmount = lostStake * (DualgaleRecoverPercent / 100m);
            Console.WriteLine($"[DUALGALE DEBUG] Valor a recuperar: {recoverAmount} ({DualgaleRecoverPercent}% de {lostStake})");

            // Use the actual payout from the winning contract as reference
            var payoutMultiplier = winningPayout / (pair.HigherProfit > 0 ? pair.HigherStake : pair.LowerStake);
            var calculatedStake = recoverAmount / payoutMultiplier;
            Console.WriteLine($"[DUALGALE DEBUG] Stake calculada antes do factor: {calculatedStake}");

            // Apply factor for progression
            calculatedStake *= DualgaleFactor;
            Console.WriteLine($"[DUALGALE DEBUG] Stake final após factor {DualgaleFactor}: {calculatedStake}");

            // Round to 2 decimal places for API compatibility
            calculatedStake = Math.Round(calculatedStake, 2);

            // Garantir que InitialStakeAmount está definido corretamente
            if (InitialStakeAmount == 0)
            {
                if (!string.IsNullOrEmpty(_originalStakeValue))
                {
                    var normalizedStake = _originalStakeValue.Replace(',', '.');
                    if (decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal originalStake))
                    {
                        InitialStakeAmount = originalStake;
                    }
                }
                else
                {
                    InitialStakeAmount = 0.40m; // Fallback
                }
                Console.WriteLine($"[DUALGALE DEBUG] InitialStakeAmount corrigido para: {InitialStakeAmount:F2}");
            }

            var roundedInitialStake = Math.Round(InitialStakeAmount, 2);

            // Garantir que roundedInitialStake não seja 0
            if (roundedInitialStake == 0)
            {
                roundedInitialStake = 0.40m; // Valor mínimo seguro
                Console.WriteLine($"[DUALGALE DEBUG] roundedInitialStake corrigido para: {roundedInitialStake:F2}");
            }

            Console.WriteLine($"[DUALGALE DEBUG] Stakes arredondadas - Calculada: {calculatedStake}, Inicial: {roundedInitialStake}");

            Console.WriteLine($"[DUALGALE DEBUG] Contrato perdedor: {pair.LosingContractType}, Nova stake: {calculatedStake:F2}, Stake inicial: {roundedInitialStake:F2}");
            _logger.LogInformation($"Dualgale: Iniciando nível {CurrentDualgaleLevel}. Contrato perdedor: {pair.LosingContractType}, Nova stake: {calculatedStake:F2}, Stake inicial: {roundedInitialStake:F2}");

            // Wait a moment for UI updates, then execute next dual entry with different stakes
            Console.WriteLine($"[DUALGALE DEBUG] Aguardando 1 segundo antes de executar próxima entrada...");
            await Task.Delay(1000);

            // Verificação thread-safe antes de executar
            lock (_dualgalePairsLock)
            {
                if (IsDualgaleProcessing)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] BLOQUEADO - Processamento iniciado durante delay");
                    _logger.LogInformation("Dualgale: Execução cancelada - processamento iniciado durante delay");
                    return;
                }

                if (!IsDualgaleEnabled)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] PARADO - Dualgale desativado durante delay");
                    _logger.LogInformation("Dualgale: Execução cancelada - sistema desativado durante delay");
                    return;
                }
            }

            // Validar stakes antes de executar
            if (calculatedStake <= 0 || roundedInitialStake <= 0)
            {
                Console.WriteLine($"[DUALGALE DEBUG] ERRO - Stakes inválidas: calculatedStake={calculatedStake}, roundedInitialStake={roundedInitialStake}");
                _logger.LogError($"Stakes inválidas calculadas: calculatedStake={calculatedStake}, roundedInitialStake={roundedInitialStake}");

                // Reset e tentar entrada inicial
                ResetDualgale();
                await Task.Delay(2000);

                if (IsDualgaleEnabled)
                {
                    Console.WriteLine($"[DUALGALE DEBUG] Tentando entrada inicial após erro de stakes...");
                    _ = Task.Run(async () => await ExecuteDualgaleEntries());
                }
                return;
            }

            // Execute next dual entry with adjusted stakes
            Console.WriteLine($"[DUALGALE DEBUG] Executando próxima entrada dupla automática...");
            await ExecuteDualgaleEntriesWithAdjustedStakes(pair.LosingContractType, calculatedStake, roundedInitialStake);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[DUALGALE DEBUG] ERRO CRÍTICO em ProcessCompletedDualgalePair: {ex.Message}");
            _logger.LogError(ex, "Erro crítico ao processar par Dualgale completado");

            // Em caso de erro crítico, tentar recuperar o sistema
            try
            {
                IsDualgaleProcessing = false; // Reset flag

                // Aguardar e tentar recuperação
                _ = Task.Run(async () =>
                {
                    await Task.Delay(3000);

                    if (IsDualgaleEnabled && !IsDualgaleProcessing)
                    {
                        var activeContracts = ProfitTableEntries.Count(entry => entry.IsActive);
                        if (activeContracts == 0)
                        {
                            Console.WriteLine($"[DUALGALE DEBUG] Tentando recuperação após erro crítico...");
                            _logger.LogInformation("Tentando recuperação do Dualgale após erro crítico");

                            // Reset para nível inicial em caso de erro crítico
                            ResetDualgale();
                            await Task.Delay(2000);

                            if (IsDualgaleEnabled)
                            {
                                await ExecuteDualgaleEntries();
                            }
                        }
                    }
                });
            }
            catch (Exception recoveryEx)
            {
                Console.WriteLine($"[DUALGALE DEBUG] ERRO na recuperação crítica: {recoveryEx.Message}");
                _logger.LogError(recoveryEx, "Erro na recuperação crítica do Dualgale");
            }
        }
        finally
        {
            // Garantir que o flag seja sempre resetado
            if (IsDualgaleProcessing)
            {
                Console.WriteLine($"[DUALGALE DEBUG] Resetando IsDualgaleProcessing no finally");
                IsDualgaleProcessing = false;
            }
        }
    }

    private void ResetDualgale()
    {
        Console.WriteLine($"[DUALGALE RESET] Iniciando reset do Dualgale...");
        Console.WriteLine($"[DUALGALE RESET] _originalStakeValue atual: '{_originalStakeValue}'");
        Console.WriteLine($"[DUALGALE RESET] InitialStakeAmount atual: {InitialStakeAmount:F2}");
        
        CurrentDualgaleLevel = 0;
        DualgaleLostStake = 0;

        // Garantir que temos um valor válido para resetar
        decimal stakeToRestore = 0;
        string stakeStringToRestore = "";

        if (!string.IsNullOrEmpty(_originalStakeValue))
        {
            var normalizedStake = _originalStakeValue.Replace(',', '.');
            if (decimal.TryParse(normalizedStake, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal originalStake) && originalStake > 0)
            {
                stakeToRestore = originalStake;
                stakeStringToRestore = _originalStakeValue;
                Console.WriteLine($"[DUALGALE RESET] Usando _originalStakeValue: {_originalStakeValue} ({originalStake:F2})");
            }
        }
        
        // Fallback 1: usar InitialStakeAmount se _originalStakeValue não estiver disponível
        if (stakeToRestore == 0 && InitialStakeAmount > 0)
        {
            stakeToRestore = InitialStakeAmount;
            stakeStringToRestore = InitialStakeAmount.ToString("F2");
            Console.WriteLine($"[DUALGALE RESET] Usando InitialStakeAmount como fallback: {InitialStakeAmount:F2}");
        }
        
        // Fallback 2: usar valor padrão se nenhum dos anteriores estiver disponível
        if (stakeToRestore == 0)
        {
            stakeToRestore = 0.40m;
            stakeStringToRestore = "0.40";
            Console.WriteLine($"[DUALGALE RESET] AVISO: Usando valor padrão 0.40 - nenhum valor original encontrado!");
            
            // Capturar este valor como original para futuras sessões
            if (string.IsNullOrEmpty(_originalStakeValue))
            {
                _originalStakeValue = stakeStringToRestore;
                Console.WriteLine($"[DUALGALE RESET] Definindo _originalStakeValue para valor padrão: {_originalStakeValue}");
            }
        }

        // Aplicar os valores restaurados
        StakeAmount = stakeStringToRestore;
        InitialStakeAmount = stakeToRestore;
        NextStakeAmount = stakeToRestore;
        
        Console.WriteLine($"[DUALGALE RESET] Valores finais - StakeAmount: '{StakeAmount}', InitialStakeAmount: {InitialStakeAmount:F2}, NextStakeAmount: {NextStakeAmount:F2}");

        // Limpar pares ativos e executar limpeza de órfãos
        lock (_dualgalePairsLock)
        {
            // Primeiro executar limpeza de órfãos
            CleanupOrphanedDualgalePairs();

            // Depois limpar todos os pares restantes
            _activeDualgalePairs.Clear();
            Console.WriteLine($"[DUALGALE RESET] Pares ativos limpos após limpeza de órfãos");
        }

        // Garantir que não há processamento em andamento
        IsDualgaleProcessing = false;
        Console.WriteLine($"[DUALGALE RESET] IsDualgaleProcessing = false");

        _logger.LogInformation($"Dualgale: Ciclo resetado para stake inicial: {InitialStakeAmount:F2}");
    }

    private void OnContractPurchased(string contractId, string contractType, string duration, decimal stake, decimal payout, DateTime purchaseTime)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var entry = new ProfitTableEntry
            {
                RefId = contractId,
                Contract = contractType,
                Duration = duration,
                EntrySpot = purchaseTime,
                Stake = stake,
                Payout = payout,
                EntryPrice = CurrentTickPrice,
                IsActive = true
            };

            ProfitTableEntries.Insert(0, entry); // Add to top of list

            // Keep only last 50 entries to avoid memory issues
            while (ProfitTableEntries.Count > 50)
            {
                ProfitTableEntries.RemoveAt(ProfitTableEntries.Count - 1);
            }

            // Update total profit
            UpdateTotalProfit();

            _logger.LogInformation($"Profit Table entry added for automatic purchase - Contract: {contractId}, Type: {contractType}");
        });
    }

    // Métodos do Watchdog para monitorar atividade do Dualgale
    private void StartDualgaleWatchdog()
    {
        lock (_watchdogLock)
        {
            StopDualgaleWatchdog(); // Parar qualquer watchdog existente

            _lastDualgaleActivity = DateTime.UtcNow;
            _dualgaleWatchdog = new Timer(CheckDualgaleActivity, null, TimeSpan.FromMinutes(2), TimeSpan.FromMinutes(1));

            Console.WriteLine("[DUALGALE WATCHDOG] Watchdog iniciado - monitorando atividade a cada 1 minuto");
            _logger.LogInformation("Dualgale Watchdog iniciado - verificação a cada 1 minuto");
        }
    }

    private void StopDualgaleWatchdog()
    {
        lock (_watchdogLock)
        {
            _dualgaleWatchdog?.Dispose();
            _dualgaleWatchdog = null;

            Console.WriteLine("[DUALGALE WATCHDOG] Watchdog parado");
            _logger.LogInformation("Dualgale Watchdog parado");
        }
    }

    private void CheckDualgaleActivity(object state)
    {
        try
        {
            if (!IsDualgaleEnabled)
            {
                return; // Não verificar se Dualgale não está ativo
            }

            var now = DateTime.UtcNow;
            var timeSinceLastActivity = now - _lastDualgaleActivity;
            var activeContracts = ProfitTableEntries.Count(entry => entry.IsActive);

            Console.WriteLine($"[DUALGALE WATCHDOG] Verificação: Última atividade há {timeSinceLastActivity.TotalMinutes:F1} minutos, Contratos ativos: {activeContracts}, Processing: {IsDualgaleProcessing}");

            // Executar limpeza de pares órfãos a cada verificação
            CleanupOrphanedDualgalePairs();

            // Se não há contratos ativos, não está processando e passou muito tempo sem atividade
            if (activeContracts == 0 && !IsDualgaleProcessing && timeSinceLastActivity.TotalMinutes > 3)
            {
                Console.WriteLine($"[DUALGALE WATCHDOG] ALERTA: Dualgale inativo há {timeSinceLastActivity.TotalMinutes:F1} minutos - reiniciando...");
                _logger.LogWarning($"Dualgale Watchdog: Sistema inativo há {timeSinceLastActivity.TotalMinutes:F1} minutos - reiniciando automaticamente");

                // Reiniciar Dualgale de forma assíncrona
                Application.Current.Dispatcher.BeginInvoke(new Action(async () =>
                {
                    try
                    {
                        // Reset estado
                        IsDualgaleProcessing = false;

                        // Aguardar um momento
                        await Task.Delay(2000);

                        // Verificar se ainda está ativo
                        if (IsDualgaleEnabled && !IsDualgaleProcessing)
                        {
                            Console.WriteLine("[DUALGALE WATCHDOG] Executando entrada automática de recuperação...");
                            _logger.LogInformation("Dualgale Watchdog: Executando entrada automática de recuperação");

                            // Atualizar timestamp de atividade
                            _lastDualgaleActivity = DateTime.UtcNow;

                            // Executar entrada
                            await ExecuteDualgaleEntries();
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[DUALGALE WATCHDOG] ERRO na recuperação automática: {ex.Message}");
                        _logger.LogError(ex, "Erro na recuperação automática do Dualgale Watchdog");
                    }
                }));
            }
            else if (activeContracts > 0 || IsDualgaleProcessing)
            {
                // Atualizar timestamp se há atividade
                _lastDualgaleActivity = now;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[DUALGALE WATCHDOG] ERRO na verificação: {ex.Message}");
            _logger.LogError(ex, "Erro no Dualgale Watchdog");
        }
    }

    // Método para atualizar atividade manualmente
    private void UpdateDualgaleActivity()
    {
        _lastDualgaleActivity = DateTime.UtcNow;
    }

    // Método para limpar pares órfãos que podem estar causando problemas
    private void CleanupOrphanedDualgalePairs()
    {
        try
        {
            lock (_dualgalePairsLock)
            {
                var now = DateTime.Now;
                var pairsToRemove = new List<string>();

                foreach (var kvp in _activeDualgalePairs)
                {
                    var pair = kvp.Value;
                    var pairAge = now - pair.CreatedAt;

                    // Remover pares muito antigos (mais de 10 minutos) que podem estar órfãos
                    if (pairAge.TotalMinutes > 10)
                    {
                        Console.WriteLine($"[DUALGALE CLEANUP] Removendo par órfão: Higher={pair.HigherContractId}, Lower={pair.LowerContractId}, Idade={pairAge.TotalMinutes:F1}min");
                        _logger.LogWarning($"Removendo par Dualgale órfão com idade de {pairAge.TotalMinutes:F1} minutos");
                        pairsToRemove.Add(kvp.Key);
                    }
                    // Remover pares onde ambos os contratos já finalizaram mas não foram processados
                    else if (pair.BothFinished)
                    {
                        Console.WriteLine($"[DUALGALE CLEANUP] Removendo par finalizado não processado: Higher={pair.HigherContractId}, Lower={pair.LowerContractId}");
                        _logger.LogWarning($"Removendo par Dualgale finalizado não processado");
                        pairsToRemove.Add(kvp.Key);
                    }
                }

                foreach (var key in pairsToRemove)
                {
                    _activeDualgalePairs.Remove(key);
                }

                if (pairsToRemove.Count > 0)
                {
                    Console.WriteLine($"[DUALGALE CLEANUP] Removidos {pairsToRemove.Count} pares órfãos. Pares restantes: {_activeDualgalePairs.Count}");
                    _logger.LogInformation($"Limpeza Dualgale: Removidos {pairsToRemove.Count} pares órfãos. Pares ativos restantes: {_activeDualgalePairs.Count}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[DUALGALE CLEANUP] ERRO na limpeza: {ex.Message}");
            _logger.LogError(ex, "Erro na limpeza de pares Dualgale órfãos");
        }
    }
}