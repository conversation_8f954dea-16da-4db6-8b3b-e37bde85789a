using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Excalibur.Models
{
    public class ProfitTableEntry : INotifyPropertyChanged
    {
        private string _refId = string.Empty;
        private string _contract = string.Empty;
        private string _duration = string.Empty;
        private DateTime? _entrySpot;
        private DateTime? _exitSpot;
        private decimal _stake;
        private decimal _totalProfitLoss;
        private bool _isActive = true;
        private decimal _currentPrice;
        private decimal _payout;
        private decimal _entryPrice;
        private decimal _exitPrice;

        // Novos campos para estatísticas
        private static int _globalSessionCount = 0;
        private static decimal _globalMaxStakeUsed = 0;
        private static int _globalMaxStakeSession = 0;
        private static int _globalMaxLevel = 0;
        private static decimal _globalTotalProfit = 0;

        private int _sessionNumber;
        private decimal _maxStakeUsed;
        private int _maxStakeSession;
        private int _maxLevel;

        public string RefId
        {
            get => _refId;
            set { _refId = value; OnPropertyChanged(); }
        }

        public string Contract
        {
            get => _contract;
            set { _contract = value; OnPropertyChanged(); }
        }

        public string Duration
        {
            get => _duration;
            set { _duration = value; OnPropertyChanged(); }
        }

        public DateTime? EntrySpot
        {
            get => _entrySpot;
            set { _entrySpot = value; OnPropertyChanged(); OnPropertyChanged(nameof(EntrySpotDisplay)); }
        }

        public DateTime? ExitSpot
        {
            get => _exitSpot;
            set { _exitSpot = value; OnPropertyChanged(); OnPropertyChanged(nameof(ExitSpotDisplay)); }
        }

        public decimal Stake
        {
            get => _stake;
            set { _stake = value; OnPropertyChanged(); OnPropertyChanged(nameof(StakeDisplay)); }
        }

        public decimal TotalProfitLoss
        {
            get => _totalProfitLoss;
            set 
            { 
                _totalProfitLoss = value; 
                OnPropertyChanged(); 
                OnPropertyChanged(nameof(TotalProfitLossDisplay));
                OnPropertyChanged(nameof(ProfitLossColor));
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set { _isActive = value; OnPropertyChanged(); }
        }

        public decimal CurrentPrice
        {
            get => _currentPrice;
            set { _currentPrice = value; OnPropertyChanged(); UpdateProfitLoss(); }
        }

        public decimal Payout
        {
            get => _payout;
            set { _payout = value; OnPropertyChanged(); }
        }

        public decimal EntryPrice
        {
            get => _entryPrice;
            set { _entryPrice = value; OnPropertyChanged(); }
        }

        public decimal ExitPrice
        {
            get => _exitPrice;
            set { _exitPrice = value; OnPropertyChanged(); }
        }

        // Propriedades para os novos campos estatísticos
        public int SessionNumber
        {
            get => _sessionNumber;
            set { _sessionNumber = value; OnPropertyChanged(); }
        }

        public decimal MaxStakeUsed
        {
            get => _maxStakeUsed;
            set { _maxStakeUsed = value; OnPropertyChanged(); }
        }

        public int MaxStakeSession
        {
            get => _maxStakeSession;
            set { _maxStakeSession = value; OnPropertyChanged(); }
        }

        public int MaxLevel
        {
            get => _maxLevel;
            set { _maxLevel = value; OnPropertyChanged(); }
        }

        // Propriedades estáticas para valores globais
        public static int GlobalSessionCount
        {
            get => _globalSessionCount;
            set => _globalSessionCount = value;
        }

        public static decimal GlobalMaxStakeUsed
        {
            get => _globalMaxStakeUsed;
            set => _globalMaxStakeUsed = value;
        }

        public static int GlobalMaxStakeSession
        {
            get => _globalMaxStakeSession;
            set => _globalMaxStakeSession = value;
        }

        public static int GlobalMaxLevel
        {
            get => _globalMaxLevel;
            set => _globalMaxLevel = value;
        }

        public static decimal GlobalTotalProfit
        {
            get => _globalTotalProfit;
            set => _globalTotalProfit = value;
        }

        // Display properties
        public string EntrySpotDisplay => EntrySpot?.ToString("HH:mm:ss") ?? "---";
        public string ExitSpotDisplay => ExitSpot?.ToString("HH:mm:ss") ?? "---";
        public string StakeDisplay => Stake.ToString("F2");
        public string TotalProfitLossDisplay => TotalProfitLoss.ToString("F2");
        public string ProfitLossColor => TotalProfitLoss >= 0 ? "#FF2ECC71" : "#FFFF4444";

        // Métodos estáticos para gerenciar estatísticas globais
        public static void UpdateGlobalStats(decimal stake, int level, decimal totalProfit)
        {
            _globalSessionCount++;

            if (stake > _globalMaxStakeUsed)
            {
                _globalMaxStakeUsed = stake;
                _globalMaxStakeSession = _globalSessionCount;
            }

            if (level > _globalMaxLevel)
            {
                _globalMaxLevel = level;
            }

            _globalTotalProfit = totalProfit;
        }

        public static void ResetGlobalStats()
        {
            _globalSessionCount = 0;
            _globalMaxStakeUsed = 0;
            _globalMaxStakeSession = 0;
            _globalMaxLevel = 0;
            // Não resetar _globalTotalProfit para manter o valor acumulado
        }

        private void UpdateProfitLoss()
        {
            if (IsActive && CurrentPrice > 0 && Payout > 0)
            {
                // For binary options, the profit/loss calculation is different
                // We'll use a simplified approach here - the real calculation will come from the API
                // This is just for visual feedback during active contracts
                var priceMovement = CurrentPrice - EntryPrice;
                var estimatedProfit = priceMovement > 0 ? (Payout - Stake) : -Stake;

                // Only update if we don't have the final result yet
                if (IsActive)
                {
                    TotalProfitLoss = estimatedProfit;
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
